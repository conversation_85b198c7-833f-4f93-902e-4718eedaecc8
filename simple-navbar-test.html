<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Navbar Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #000;
            color: #fff;
            height: 200vh;
        }
        
        .overlay {
            height: 0%;
            width: 100%;
            position: fixed;
            z-index: 999;
            top: 0;
            right: 0;
            background-color: rgba(0, 0, 0, 0.9);
            overflow-y: hidden;
            transition: height 0.5s ease;
        }
        
        .overlay-content {
            position: relative;
            top: 25%;
            width: 100%;
            text-align: center;
            margin-top: 30px;
        }
        
        .overlay a {
            padding: 8px;
            text-decoration: none;
            font-size: 36px;
            color: #818181;
            display: block;
            transition: 0.3s;
        }
        
        .closebtn {
            position: absolute;
            top: 20px;
            right: 45px;
            font-size: 60px;
            cursor: pointer;
            color: #fff;
        }
        
        .hamberger-menu {
            cursor: pointer;
            padding: 10px;
            background: #333;
            color: #fff;
            border: none;
            border-radius: 4px;
            display: inline-block;
            margin: 20px;
        }
    </style>
</head>
<body>
    <h1>Simple Navbar Test</h1>
    
    <span class="hamberger-menu" onclick="console.log('Click detected!'); openNav();">
        ☰ Open Menu
    </span>
    
    <div id="myNav" class="overlay">
        <a href="javascript:void(0)" class="closebtn" onclick="console.log('Close clicked!'); closeNav();">&times;</a>
        <div class="overlay-content">
            <a href="#">Home</a>
            <a href="#">About</a>
            <a href="#">Services</a>
            <a href="#">Contact</a>
        </div>
    </div>
    
    <p>This is a simple test to verify the navbar functionality works. Click the menu button above.</p>
    <p>Scroll down to test scroll-lock behavior...</p>
    
    <div style="height: 100vh; background: linear-gradient(45deg, #333, #666); margin: 20px 0; padding: 20px;">
        <h2>Scrollable Content</h2>
        <p>This content should not be scrollable when the menu is open.</p>
    </div>

    <script>
        console.log('Script loading...');
        
        function openNav() {
            console.log('openNav() function called');
            const overlay = document.getElementById("myNav");
            console.log('Overlay element:', overlay);
            
            if (overlay) {
                console.log('Setting overlay height to 100%');
                overlay.style.height = "100%";
                
                // Disable scrolling
                document.body.style.overflow = 'hidden';
                document.documentElement.style.overflow = 'hidden';
                console.log('Scrolling disabled');
            } else {
                console.error('Overlay element not found!');
            }
        }

        function closeNav() {
            console.log('closeNav() function called');
            const overlay = document.getElementById("myNav");
            console.log('Overlay element:', overlay);
            
            if (overlay) {
                console.log('Setting overlay height to 0%');
                overlay.style.height = "0%";
                
                // Enable scrolling
                document.body.style.overflow = '';
                document.documentElement.style.overflow = '';
                console.log('Scrolling enabled');
            } else {
                console.error('Overlay element not found!');
            }
        }
        
        console.log('Script loaded successfully');
        
        // Test if elements exist
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            const overlay = document.getElementById("myNav");
            const hamburger = document.querySelector(".hamberger-menu");
            console.log('Elements check:', {
                overlay: !!overlay,
                hamburger: !!hamburger
            });
        });
    </script>
</body>
</html>
