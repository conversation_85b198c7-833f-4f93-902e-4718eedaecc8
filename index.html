<!DOCTYPE html>
<html lang="en">

<head>
    <!--====== Required meta tags ======-->
    <meta charset="utf-8" />
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <meta name="description" content="" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <!--====== Title ======-->
    <title> Electric Marshmallow </title>
    <!--====== Favicon Icon ======-->
    <link rel="shortcut icon" href="assets/img/favicon.ico" type="img/png" />
    <!--====== Animate Css ======-->
    <link rel="stylesheet" href="assets/css/animate.min.css">
    <!--====== Bootstrap css ======-->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css" />
    <!--====== Fontawesome css ======-->
    <!-- <link rel="stylesheet" href="assets/css/font-awesome.min.css" /> -->
    <!--====== Flaticon css ======-->
    <link rel="stylesheet" href="assets/css/flaticon.css" />
    <!--====== Jquery ui ======-->
    <link rel="stylesheet" href="assets/css/jquery-ui.min.css" />
    <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.5.9/slick.min.css'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/malihu-custom-scrollbar-plugin/3.1.5/jquery.mCustomScrollbar.min.css'>
    <!--====== Style css ======-->
    <link rel="stylesheet" href="assets/css/electric.css" />
    <!--====== GSAP CDN ======-->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
</head>

<body>

    <div id="myNav" class="overlay">
      <a href="javascript:void(0)" class="closebtn" onclick="closeNav()">&times;</a>
      <div class="overlay-content">
        <div class="left-menu">
          <ul>
            <li class="active"> <a href="#">Our Work</a> </li>
            <li> <a href="#">About us</a> </li>
            <li> <a href="#">Mission & Values</a> </li>
            <li> <a href="#">inclusivity & Impact</a> </li>
            <li> <a href="#">Contact Us</a> </li>
            <li> <a href="#">Blogs</a> </li>

          </ul>
        </div>
        <div class="right-menu">
          <div class="right-menu-col-1">
            <img src="assets/img/nav-img-1.png">
          </div>
          <div class="right-menu-col-2">
            <img src="assets/img/nav-img-2.png">
          </div>
        </div>
      </div>
    </div>
  
    <!--====== Header Section start ======-->
    <header class="header-three sticky-header">
        <!-- Header Menu  -->
        <div class="header-nav">
            <div class="container">
                <div class="nav-container align-items-center justify-content-self">
                    <!-- Site Logo -->

                    <div class="site-logo">
                        <span class="hamberger-menu" onclick="openNav()"> <img src="assets/img/hamberger-menu-icon.png"> </span>
                        <a href="#"><img src="assets/img/logo.png" alt="Logo"></a>
                    </div>

                    <div class="header-right-icon">
                      <ul>
                        <li> <a href="#"> <img src="assets/img/instagram.png"> </a> </li>
                        <li> <a href="#"> <img src="assets/img/vain.png"> </a> </li>
                        <li> <a href="#"> <img src="assets/img/mail.png"> </a> </li>
                      </ul>
                    </div>
                    
                </div>
            </div>
        </div>
    </header>
    <!--====== Header Section end ======-->

    <!--====== Scroll Animation Container Start ======-->
    <div class="scroll-container">
        <!--====== CTA Start ======-->
        <section class="middile-banner section-gap ">
            <div class="container">

                    <div class="row justify-content-center">
                        <div class="col-lg-12 col-md-12 col-sm-12">
                            <div class="middile-banner-box text-center">
                                <div class="sLider">
                                    <div class="slide slide1">
                                      <div class="slideContent">
                                       <img src="assets/img/logo.png">
                                      </div>
                                    </div>
                                    <div class="slide slide2">
                                      <div class="slideContent">
                                        <img src="assets/img/logo.png">
                                      </div>
                                    </div>
                                    <div class="slide slide3">
                                      <div class="slideContent">
                                        <img src="assets/img/logo.png">
                                      </div>
                                    </div>
                                    <div class="slide slide4">
                                      <div class="slideContent">
                                        <img src="assets/img/logo.png">
                                      </div>
                                    </div>

                                    <div class="slide slide5">
                                      <div class="slideContent">
                                        <img src="assets/img/logo.png">
                                      </div>
                                    </div>

                                  </div>

                            </div>
                        </div>
                    </div>

            </div>

            <!-- Scroll to View Indicator -->
            <div class="scroll-indicator">
                <div class="scroll-text">Scroll to view more</div>
                <div class="scroll-arrow">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 16L6 10H18L12 16Z" fill="currentColor"/>
                    </svg>
                </div>
            </div>
        </section>
        <!--====== CTA End ======-->

    <!--====== About Section start ======-->
    <section class="fixed-section">
        <div class="container-fluid">
            <div class="row align-items-center justify-content-center">
                <div class="col-lg-12 col-md-12 p-0">
                    <!-- <div class="fixed-full" style="background: url(assets/img/full-banner-video.gif);">
                        
                    </div> -->
                </div>
                

            </div>
        </div>
    </section>
    <!--====== About Section end ======-->
    <!-- mobile section of electric marshmallow-->
    
    <!--====== About Section start ======-->
    <section class="about-section-three section-gap about-section-bg-after">
        <div class="container">
            <div class="row align-items-center justify-content-center">
                
                <div class="col-lg-8 col-md-8 pl-md-4">
                    <div class="about-text-three text-center">
                        <div class="section-title left-border mb-30">
                            <h2 class="title"> Electric Marshmallow </h2>
                        </div>
                        <div class="main-content-img wow fadeInUp" style="position:relative; display:flex; justify-content:center;">
                          <img 
  id="aboutVideo"
  src="assets/img/full-banner-video.gif" 
  alt="Electric Marshmallow Video"
  style="
    width:320px;
    height:180px;
    border-radius:16px;
    box-shadow:0 8px 32px rgba(0,0,0,0.3);
    background:#000;
    transition:transform 0.35s cubic-bezier(.5,1.5,.5,1),opacity 0.2s;
    will-change:transform,opacity;
    display:block;
    margin:0 auto;
    object-fit:cover;
    transform-origin: center top;
  "
>
                      </div>
                         <p class="mb-10 para">
                            Electric Marshmallow Productions is the brainchild of screenwriter, Ashtyn Law, and director, activist, and performer, Dom Evans. Growing up poor in the Midwest meant they both spent an inordinate event of time watching television and dreaming of a better future for themselves and others like them.

                        </p>

            
                        <a href="#" class="main-btn" target="_blank">  LEARN MORE <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640"><!--!Font Awesome Free v7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M566.6 342.6C579.1 330.1 579.1 309.8 566.6 297.3L406.6 137.3C394.1 124.8 373.8 124.8 361.3 137.3C348.8 149.8 348.8 170.1 361.3 182.6L466.7 288L96 288C78.3 288 64 302.3 64 320C64 337.7 78.3 352 96 352L466.7 352L361.3 457.4C348.8 469.9 348.8 490.2 361.3 502.7C373.8 515.2 394.1 515.2 406.6 502.7L566.6 342.7z"/></svg> </a>

                        <div class="folting-box folting-left"> <img src="assets/img/floting-left.png" alt="Image" class="image-one wow fadeInLeft"
                            data-wow-duration="1800ms" data-wow-delay="800ms">
                        </div>

                        <div class="folting-box folting-right"> <img src="assets/img/floting-right.png" alt="Image" class="image-one wow fadeInRight"
                            data-wow-duration="2000ms" data-wow-delay="1000ms">
                        </div>

                    </div>
                </div>
                
            </div>
        </div>
    </section>
    <!--====== About Section end ======-->

    <!--====== Mobile Section (Inside Scroll Container for Proper Layering) ======-->
    <section class="mobile-section-wrapper">
        <div class="mob">
            <h2> ELECTRIC MARSHMALLOW </h2>
            <img src="assets/img/full-banner-video.gif" alt="">
            <p>Electric Marshmallow Productions is the brainchild of screenwriter, Ashtyn Law, and director, activist, and performer, Dom Evans. Growing up poor in the Midwest meant they both spent an inordinate event of time watching television and dreaming of a better future for themselves and others like them.</p>
            <button>Learn More → </button>
            
            <!-- Mobile Floating Images - Now Children of .mob -->
            <div class="mob-floating-left">
                <img src="assets/img/floting-left.png" alt="">
            </div>
            <div class="mob-floating-right">
                <img src="assets/img/floting-right.png" alt="">
            </div>
        </div>
    </section>
    <!--====== Mobile Section End ======-->
    </div>
    <!--====== Scroll Animation Container End ======-->

    <!--====== About Section start ======-->
    <section class="section-gap mhid">
        <div class="container-fluid">
            <div class="row align-items-center justify-content-center">
                <div class="col-lg-12 col-md-12 p-0">
                    <div class="full-gallery">
                        <img src="assets/img/full-banner-video.gif" alt="Image" class="image-one wow fadeInRight"
                            data-wow-duration="1500ms" data-wow-delay="400ms">
                        
                    </div>
                </div>
                

            </div>
        </div>
    </section>
    <!--====== About Section end ======-->

    <!--====== Our Work Section start ======-->
    <!--====== Our Work Section end ======-->

    <!-- partial:index.partial.html -->

    <!-- <main class="scroll-container">
      <section class="full-scroll" data-index="1">
        <div class="container">
            <div class="row align-items-center justify-content-center">
                <div class="col-lg-6 col-md-6">
                    <div class="left-scroll-box">
                      <h2> Space Pirates </h2>
                      <ul>
                        <li> <span class="text-grey"> Directed By: </span> Dom Evans </li>
                        <li> <span class="text-grey"> Screenwriter: </span> Ashtyn Law </li>
                      </ul>
                      <a href="#" class="main-btn" target="_blank"> View Project <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640"> <path d="M566.6 342.6C579.1 330.1 579.1 309.8 566.6 297.3L406.6 137.3C394.1 124.8 373.8 124.8 361.3 137.3C348.8 149.8 348.8 170.1 361.3 182.6L466.7 288L96 288C78.3 288 64 302.3 64 320C64 337.7 78.3 352 96 352L466.7 352L361.3 457.4C348.8 469.9 348.8 490.2 361.3 502.7C373.8 515.2 394.1 515.2 406.6 502.7L566.6 342.7z"/></svg> </a>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6">
                    <div class="right-scroll-box">
                      <img src="assets/img/scroll-img-1.png">
                    </div>
                </div>
            </div>
        </div>
      </section>
      <section class="full-scroll" data-index="2">
        <div class="container">
            <div class="row align-items-center justify-content-center">
                <div class="col-lg-6 col-md-6">
                    <div class="left-scroll-box">
                      <h2> The Park </h2>
                      <ul>
                        <li> <span class="text-grey"> Directed By: </span> Dom Evans </li>
                        <li> <span class="text-grey"> Screenwriter: </span> Ashtyn Law </li>
                      </ul>
                      <a href="#" class="main-btn" target="_blank"> View Project <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640"> <path d="M566.6 342.6C579.1 330.1 579.1 309.8 566.6 297.3L406.6 137.3C394.1 124.8 373.8 124.8 361.3 137.3C348.8 149.8 348.8 170.1 361.3 182.6L466.7 288L96 288C78.3 288 64 302.3 64 320C64 337.7 78.3 352 96 352L466.7 352L361.3 457.4C348.8 469.9 348.8 490.2 361.3 502.7C373.8 515.2 394.1 515.2 406.6 502.7L566.6 342.7z"/></svg> </a>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6">
                    <div class="right-scroll-box">
                      <img src="assets/img/scroll-img-2.png">
                    </div>
                </div>
            </div>
        </div>
      </section>
      
    </main> -->
    <!-- partial -->

    <!-- Our Work Content Transition Section -->
    <section class="work-transition-section section-gap-none">
      <div class="work-sticky-container">
        <div class="work-section-title">
       
              <div class="divider-section">
        <div class="container">
            <div class="row align-items-center justify-content-center">
                <div class="col-lg-12 col-md-12">
                    <div class="title-big">
                      <h2> Our Work </h2>
                    </div>
                </div>
            </div>
        </div>
      </div>
        </div>
        
        <div class="work-content-container" id="work-content-container">
          <!-- Project 1: Space Pirates -->
          <div class="work-project" id="project-1">
            <div class="work-content-grid">
              <div class="work-content-left">
                <div class="title-area">
                  <h2 class="project-title">Space Pirates</h2>
                </div>
                <ul class="project-credits">
                  <li><span class="text-grey">Directed By:</span> Dom Evans</li>
                  <li><span class="text-grey">Screenwriter:</span> Ashtyn Law</li>
                </ul>
                <div class="project-btn-wrap">
                  <a class="project-btn" href="#0">
                    <span class="btn-text">View Project →</span>
                  </a>
                </div>
              </div>
              <div class="work-content-right">
                <div class="project-image-container">
                  <img src="assets/img/scroll-img-1.png" alt="Space Pirates" class="project-image">
                  <div class="view-overlay">
                    <span class="view-text">VIEW</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Project 2: The Park -->
          <div class="work-project" id="project-2">
            <div class="work-content-grid">
              <div class="work-content-left">
                <div class="title-area">
                  <h2 class="project-title">The Park</h2>
                </div>
                <ul class="project-credits">
                  <li><span class="text-grey">Directed By:</span> Dom Evans</li>
                  <li><span class="text-grey">Screenwriter:</span> Ashtyn Law</li>
                </ul>
                <div class="project-btn-wrap">
                  <a class="project-btn" href="#0">
                    <span class="btn-text">View Project →</span>
                  </a>
                </div>
              </div>
              <div class="work-content-right">
                <div class="project-image-container">
                  <img src="assets/img/scroll-img-2.png" alt="The Park" class="project-image">
                  <div class="view-overlay">
                    <span class="view-text">VIEW</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>


        <script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js'></script>


<script type="text/javascript">
    $(document).ready(function() {
    var swiper = new Swiper(".swiper-container-h", {
            direction: "horizontal",
            effect: "slide",
            autoplay: {
            longSwipes: true,
            autoplay:4000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true
        },
            parallax: true,
            speed: 2600,
            rtl: true,
            loop: true,
            loopFillGroupWithBlank: !0,
  
            // mousewheel: {
            //   eventsTarged: ".swiper-slide",
            //   sensitivity: 10
            // }, 
            keyboard: {
              enabled: true,
              onlyInViewport: true
            },
            scrollbar: {
              el: ".swiper-scrollbar",
              hide: false,
              draggable: true
            },
            navigation: {
              nextEl: ".swiper-button-next",
              prevEl: ".swiper-button-prev",
            },
            pagination: {
                el: ".swiper-pagination",
                type: "progressbar"
              }
          });
        var swiper = new Swiper(".swiper-container-h1", {
            direction: "horizontal",
            effect: "slide",
            autoplay: false,
            parallax: true,
            speed: 2600,
            rtl: true,
            loop: true,
            loopFillGroupWithBlank: !0,
            ongSwipes: true,
            autoplay:2000,
            autoplayDisableOnInteraction:true,
            keyboard: {
              enabled: true,
              onlyInViewport: true
            },
            scrollbar: {
              el: ".swiper-scrollbar",
              hide: false,
              draggable: true
            },
            navigation: {
              nextEl: ".swiper-button-next",
              prevEl: ".swiper-button-prev",
            },
            pagination: {
                el: ".swiper-pagination",
                type: "bullets",
                clickable:"true"
              }
          });
});

  // Dynamic Viewport Height Handler for Mobile
  function setViewportHeight() {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
  }

  // GSAP ScrollTrigger Animation Implementation
  document.addEventListener('DOMContentLoaded', function() {
    // Register ScrollTrigger plugin
    gsap.registerPlugin(ScrollTrigger);

    // Initialize dynamic viewport height
    setViewportHeight();
    window.addEventListener('resize', setViewportHeight);
    window.addEventListener('orientationchange', setViewportHeight);

    const heroSection = document.querySelector('.middile-banner');
    const aboutSection = document.querySelector('.about-section-three');
    const video = document.getElementById('aboutVideo');
    const mobileSection = document.querySelector('.mobile-section-wrapper');

    if (heroSection && aboutSection && video) {
      // Function to initialize animations
      function initScrollAnimations() {
        // Check if device supports smooth animations (avoid on low-end devices)
        const isMobile = window.innerWidth <= 768;
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

        // Initialize masterTimeline variable to avoid scope issues
        let masterTimeline;

        if (prefersReducedMotion) {
          // Skip complex animations for users who prefer reduced motion
          return;
        }

        // Mobile Section Visibility Management
        if (mobileSection) {
          if (isMobile) {
            // Mobile: Keep as normal positioned section, just control visibility
            gsap.set(mobileSection, {
              display: "block",
              position: "relative", // Normal document flow, NOT fixed like desktop
              top: "auto",
              left: "auto",
              width: "100%",
              height: "auto", // Let it size naturally
              zIndex: "auto", // Normal z-index
              opacity: 0 // Start hidden, will show after animation
            });
            console.log("Mobile section enabled as normal positioned section");
          } else {
            // Hide mobile section on desktop
            gsap.set(mobileSection, {
              display: "none"
            });
            console.log("Mobile section hidden for desktop devices");
          }
        }

        // Step 1: Set up proper initial positioning using fixed for better curtain control
        // About section should be a fixed background that stays stationary during hero exit
        gsap.set(aboutSection, {
          position: "fixed",
          top: 0,
          left: 0,
          width: "100%",
          height: "100vh",
          zIndex: 20, // Background layer - below hero section
          opacity: 1  // Start fully visible
        });

        // Hero section should be the foreground "curtain"
        gsap.set(heroSection, {
          position: "relative",
          zIndex: 30 // Foreground layer - above about section
        });

        console.log("Initial setup complete - about section as fixed background, hero as foreground curtain");

        // Immediately ensure all subsequent sections are visible with proper styling
        const allSubsequentSections = document.querySelectorAll('.scroll-animation-wrapper ~ section');
        allSubsequentSections.forEach((section, index) => {
          gsap.set(section, {
            position: "relative",
            zIndex: 300 + index, // High z-index above animation elements
            background: "#000", // Black background to match site theme
            backgroundColor: "black",
            visibility: "visible",
            display: "block"
          });

          // Also apply via direct style for extra assurance
          section.style.position = "relative";
          section.style.zIndex = (300 + index).toString();
          section.style.background = "#000";
          section.style.backgroundColor = "black";
          section.style.visibility = "visible";
          section.style.display = "block";
        });

        console.log(`Applied proper styling to ${allSubsequentSections.length} subsequent sections with z-index 300+`);

        // Step 2: Create master timeline - MOBILE: Natural scroll behavior | DESKTOP: Parallax pinning
        if (isMobile) {
          // MOBILE: Parallax shutter effect with natural scrolling
          console.log("Mobile: Using parallax shutter effect with natural scroll progression");

          // Mobile: Set about section as fixed background for shutter effect
          gsap.set(aboutSection, {
            position: "fixed", // Fixed background for parallax effect
            top: 0,
            left: 0,
            width: "100%",
            height: "100vh",
            zIndex: 20, // Background layer - below hero section
            opacity: 1 // Start fully visible as background
          });

          // Mobile: Hero section as foreground curtain
          gsap.set(heroSection, {
            position: "relative",
            zIndex: 30 // Foreground layer - above about section
          });

          // Mobile: Parallax shutter animation with natural scroll progression
          gsap.to(heroSection, {
            yPercent: -100, // Move hero section (curtain) up and out
            ease: "power2.inOut",
            scrollTrigger: {
              trigger: heroSection,
              start: "top top",
              end: "bottom top", // Natural scroll distance - no extended areas
              scrub: 1, // Responsive but smooth
              onUpdate: (self) => {
                const progress = self.progress;

                // Keep about section fixed only while hero section is overlapping (0% - 90%)
                if (progress < 0.9) {
                  // Hero section is still overlapping - keep about section fixed
                  gsap.set(aboutSection, {
                    position: "fixed",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100vh",
                    zIndex: 20,
                    opacity: 1
                  });
                } else {
                  // Hero section has moved away - convert about section to normal flow
                  gsap.set(aboutSection, {
                    position: "relative",
                    top: "auto",
                    left: "auto",
                    width: "100%",
                    height: "auto",
                    zIndex: "auto",
                    opacity: 1
                  });
                }
              },
              onComplete: () => {
                console.log("Mobile: Shutter effect complete - about section in normal flow");
                // Ensure about section is in normal document flow
                gsap.set(aboutSection, {
                  position: "relative",
                  top: "auto",
                  left: "auto",
                  width: "100%",
                  height: "auto",
                  zIndex: "auto",
                  opacity: 1
                });

                // Show mobile section
                gsap.to(mobileSection, {
                  opacity: 1,
                  duration: 0.5,
                  ease: "power2.out"
                });
              }
            }
          });

        } else {
          // DESKTOP: Original parallax behavior with pinning
          const scrollDistance = "+=400vh";
          const scrubSpeed = 1.5;

          masterTimeline = gsap.timeline({
            scrollTrigger: {
              trigger: heroSection,
              start: "top top",
              end: scrollDistance,
              scrub: scrubSpeed,
              pin: true,
              pinSpacing: false,
              pinType: "transform",
              anticipatePin: 1,
              invalidateOnRefresh: true,
              onUpdate: (self) => {
                const progress = self.progress;
                if (Math.round(progress * 100) % 10 === 0) {
                  console.log(`Desktop timeline progress: ${Math.round(progress * 100)}%`);
                }
              }
            }
          });

          // DESKTOP: Phase 1 - Curtain Lifting Effect
          const curtainDuration = 1;
          masterTimeline.to(heroSection, {
            yPercent: -100, // Move hero section (curtain) completely up and out
            ease: "none",
            duration: curtainDuration,
            onReverseComplete: () => {
              console.log("Desktop: Hero curtain animation reversed - preparing about section");
              gsap.set(aboutSection, {
                position: "fixed",
                top: 0,
                left: 0,
                width: "100%",
                height: "100vh",
                zIndex: 20,
                opacity: 1
              });
            }
          });

          // DESKTOP: Phase 2 - Extended Pause/Breathing Space
          const pauseDuration = 2;
          masterTimeline.to({}, {
            duration: pauseDuration,
          }, curtainDuration); // Start after curtain phase

          // DESKTOP: Phase 3 - Video Scaling Animation
          const targetWidthPercent = 0.8;
          const getMaxScale = () => {
            const currentWidth = video.getBoundingClientRect().width || 320;
            const targetWidth = window.innerWidth * targetWidthPercent;
            return targetWidth / currentWidth;
          };

          masterTimeline.to(video, {
            scale: () => getMaxScale(),
            transformOrigin: "center center",
            y: () => {
              // Calculate offset to keep video centered in viewport during scaling
              const videoRect = video.getBoundingClientRect();
              const viewportCenter = window.innerHeight / 2;
              const videoCenter = videoRect.top + (videoRect.height / 2);
              return viewportCenter - videoCenter;
            },
            ease: "power2.out",
            duration: 0.5, // 12.5% of timeline (0.5/4) - quick video scaling
            onStart: () => {
              console.log("Video scaling animation started - desktop only");
              gsap.set(video, {
                zIndex: 9999,
                position: "relative",
                transformOrigin: "center center"
              });
            },
            onComplete: () => {
              console.log(`Video scaled to ${targetWidthPercent * 100}vw - desktop only`);
            },
            onReverseComplete: () => {
              console.log("Video scaling reversed - returned to original size");
              gsap.set(video, {
                zIndex: "auto",
                y: 0,
                transformOrigin: "center center" // Keep center center for consistency
              });
            }
          }, curtainDuration + pauseDuration); // Start after pause phase

          // DESKTOP: Phase 4 - About Section Fade Out
          const fadeStartTime = 3.5; // After video scaling
          masterTimeline.to(aboutSection, {
            opacity: 0,
            ease: "power2.in",
            duration: 0.5,
            onStart: () => {
              console.log("Desktop: About section fade out started");
              gsap.set(video, {
                zIndex: "auto"
              });
            },
            onReverseComplete: () => {
              console.log("Desktop: Fade out reversed - restoring about section");
              gsap.set(aboutSection, {
                position: "fixed",
                top: 0,
                left: 0,
                width: "100%",
                height: "100vh",
                zIndex: 20,
                opacity: 1
              });
            }
          }, fadeStartTime);

          // DESKTOP: Phase 5 - Cleanup and Restore Document Flow
          const cleanupTime = 4; // At end of timeline
          masterTimeline.call(() => {
            console.log("Desktop: Animation sequence complete - restoring document flow");
            // Desktop: Hide about section after animation
            gsap.set(aboutSection, {
              position: "relative",
              top: "auto",
              left: "auto",
              width: "auto",
              height: "auto",
              zIndex: "auto",
              opacity: 0 // Hidden on desktop
            });
            console.log("Desktop: About section hidden after animation");

            const subsequentSections = document.querySelectorAll('.scroll-animation-wrapper ~ section');
            subsequentSections.forEach((section, index) => {
              gsap.set(section, {
                position: "relative",
                zIndex: 100 + index,
                background: "#000",
                backgroundColor: "black",
                visibility: "visible",
                display: "block"
              });
            });

            console.log("Desktop: Animation sequence document flow restored");
          }, null, cleanupTime);
        } // End desktop conditional

        // Enhanced bidirectional scroll support (desktop only)
        if (masterTimeline) {
          masterTimeline.eventCallback("onReverseComplete", () => {
          console.log("Restoring initial state for reverse scroll");

          // Restore about section to initial fixed positioning with full opacity
          gsap.set(aboutSection, {
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: "100vh",
            zIndex: 20,
            opacity: 1, // Full opacity for curtain effect
            clearProps: "transform" // Clear any transforms
          });

          // Restore hero section to initial state
          gsap.set(heroSection, {
            position: "relative",
            zIndex: 30,
            yPercent: 0, // Reset hero position
            clearProps: "transform"
          });

          // Restore video to initial state with proper centering reset
          gsap.set(video, {
            scale: 1,
            y: 0, // Reset vertical offset
            zIndex: "auto",
            transformOrigin: "center center", // Keep center center for consistent scaling
            clearProps: "transform"
          });

          console.log("Initial animation state fully restored - including video reset");
        });

          // Comprehensive state management for bidirectional scrolling - Mobile optimized
          masterTimeline.eventCallback("onUpdate", () => {
          const progress = masterTimeline.progress();
          const isReversed = masterTimeline.reversed();

          if (isReversed) {
            console.log(`Reverse scroll - Progress: ${Math.round(progress * 100)}% (${isMobile ? 'Mobile' : 'Desktop'})`);

            // Mobile vs Desktop phase management during reverse scroll
            if (isMobile) {
              // Mobile-specific reverse scroll management
              if (progress <= 0.7) { // Mobile: most of the animation
                gsap.set(aboutSection, {
                  position: "fixed",
                  top: 0,
                  left: 0,
                  width: "100%",
                  height: "100vh",
                  zIndex: 20,
                  opacity: 1
                });
              }
            } else {
              // Desktop reverse scroll management (existing logic)
              if (progress <= 0.4) {
                // In curtain lifting phase during reverse
                gsap.set(aboutSection, {
                  position: "fixed",
                  top: 0,
                  left: 0,
                  width: "100%",
                  height: "100vh",
                  zIndex: 20,
                  opacity: 1
                });

                gsap.set(video, {
                  position: "relative",
                  top: "auto",
                  left: "auto",
                  xPercent: 0,
                  yPercent: 0,
                  scale: 1,
                  zIndex: "auto",
                  clearProps: "transform"
                });

              } else if (progress > 0.4 && progress <= 0.8) {
                // In extended video scaling phase during reverse
                gsap.set(aboutSection, {
                  position: "fixed",
                  top: 0,
                  left: 0,
                  width: "100%",
                  height: "100vh",
                  zIndex: 20,
                  opacity: 1
                });

              } else if (progress > 0.8) {
                // In fade out phase during reverse
                gsap.set(aboutSection, {
                  position: "fixed",
                  top: 0,
                  left: 0,
                  width: "100%",
                  height: "100vh",
                  zIndex: 20
                });

                gsap.set(video, {
                  position: "relative",
                  top: "auto",
                  left: "auto",
                  xPercent: 0,
                  yPercent: 0,
                  scale: 1,
                  zIndex: "auto",
                  clearProps: "transform"
                });
              }
            }
          }
        });

          // Simplified scroll direction monitoring - Mobile optimized
          let lastProgress = 0;
          masterTimeline.eventCallback("onUpdate", () => {
          const currentProgress = masterTimeline.progress();
          const isScrollingUp = currentProgress < lastProgress;

          // Mobile vs Desktop opacity management
          if (isMobile) {
            // Mobile: Keep about section visible during most of the animation
            if (isScrollingUp && currentProgress < 0.9) {
              const currentOpacity = parseFloat(getComputedStyle(aboutSection).opacity);
              if (currentOpacity < 1) {
                gsap.set(aboutSection, {
                  opacity: 1
                });
              }
            }
          } else {
            // Desktop: Original logic for extended video scaling phase
            if (isScrollingUp && currentProgress < 0.8) {
              const currentOpacity = parseFloat(getComputedStyle(aboutSection).opacity);
              if (currentOpacity < 1) {
                gsap.set(aboutSection, {
                  opacity: 1
                });
              }
            }
          }

            lastProgress = currentProgress;
          });
        } // End masterTimeline conditional block

        // Step 3: Create a spacer element for smooth transition to subsequent sections
        const spacerDiv = document.createElement('div');
        spacerDiv.className = 'animation-spacer';
        spacerDiv.style.height = isMobile ? '20vh' : '200vh'; // Further reduced spacer height to match ultra-short mobile scroll
        spacerDiv.style.background = 'transparent';
        spacerDiv.style.position = 'relative';
        spacerDiv.style.zIndex = '1';

        // Insert spacer after about section
        aboutSection.parentNode.insertBefore(spacerDiv, aboutSection.nextSibling);

        console.log("Animation spacer added with optimized height for smooth document flow transition");
        
        // Mobile-specific: Aggressively remove pin spacing after initialization
        // if (isMobile) {
        //   setTimeout(() => {
        //     const pinSpacers = document.querySelectorAll('.pin-spacer');
        //     pinSpacers.forEach(spacer => {
        //       spacer.style.height = '0px';
        //       spacer.style.minHeight = '0px';
        //       spacer.style.maxHeight = '0px';
        //       spacer.style.padding = '0';
        //       spacer.style.margin = '0';
        //       spacer.style.display = 'none';
        //     });
        //     console.log(`Mobile: Removed ${pinSpacers.length} pin spacer elements`);
        //   }, 100);
          
        //   // Also check and remove during scroll
        //   window.addEventListener('scroll', () => {
        //     const pinSpacers = document.querySelectorAll('.pin-spacer');
        //     pinSpacers.forEach(spacer => {
        //       if (spacer.style.height !== '0px') {
        //         spacer.style.height = '0px';
        //         spacer.style.minHeight = '0px';
        //         spacer.style.maxHeight = '0px';
        //         spacer.style.display = 'none';
        //       }
        //     });
        //   });
        // }
      }

      // Initialize animations
      initScrollAnimations();

      // Refresh ScrollTrigger on window resize for responsive behavior
      window.addEventListener('resize', () => {
        ScrollTrigger.refresh();
        
        // Update mobile section visibility on resize
        if (mobileSection) {
          const isNowMobile = window.innerWidth <= 768;
          if (isNowMobile) {
            gsap.set(mobileSection, {
              display: "block",
              position: "relative", // Normal positioning for mobile
              top: "auto",
              left: "auto",
              width: "100%",
              height: "auto",
              zIndex: "auto",
              opacity: 1
            });
          } else {
            gsap.set(mobileSection, {
              display: "none"
            });
          }
        }
      });
    }
    
    // Our Work Content Transition Animation - Enabled for desktop, disabled for mobile 300px-500px
    const workSection = document.querySelector(".work-transition-section");
    if (workSection) {
      // Check if screen is in mobile range (300px-500px)
      const isMobileRange =
        window.innerWidth >= 300 && window.innerWidth <= 500;

      if (!isMobileRange) {
        // Apply scroll animations for desktop and larger screens
        console.log("🖥️ Desktop detected - initializing scroll animations");

        // Set initial states
        gsap.set("#project-2", { opacity: 0, zIndex: 10 });

        // Elements should be available at this point

        // Create timeline for content transition
        let workTransitionTl = gsap.timeline({
          scrollTrigger: {
            trigger: ".work-transition-section",
            start: "top top",
            end: "+=350vh", // INCREASED: More scroll area for smoother, longer transitions
            scrub: 1,
            pin: ".work-sticky-container",
            anticipatePin: 1,
            invalidateOnRefresh: true,
            onEnter: () =>
              console.log("🎬 Work section scroll animation started"),
            onLeave: () =>
              console.log("🎬 Work section scroll animation completed"),
            onEnterBack: () =>
              console.log("🎬 Work section scroll animation reversed"),
            onLeaveBack: () =>
              console.log("🎬 Work section scroll animation reset"),
          },
        });

        // Add content transition animations with enhanced timing for 350vh scroll area
        workTransitionTl
          .to(
            "#project-1",
            {
              opacity: 0,
              duration: 0.6, // Longer duration for smoother transition
              ease: "power2.inOut",
            },
            0.2 // Start later in the scroll for more gradual transition
          )
          .to(
            "#project-2",
            {
              opacity: 1,
              zIndex: 25,
              duration: 1.0, // Adjusted duration for better pacing
              ease: "power2.inOut",
            },
            0.5 // More spacing between transitions
          )
          .to(
            "#project-2",
            {
              opacity: 0,
              zIndex: 25,
              duration: 0.8, // Longer duration for smoother transition
              ease: "power2.inOut",
            },
            1.8 // Position after project-2 fade in completes
          )
          // Final fade-out effect for entire section
          .to(
            ".work-sticky-container",
            {
              opacity: 0,
              duration: 1.2, // Smooth fade-out duration
              ease: "power2.inOut",
              onStart: () => console.log("🌅 Starting final fade-out of Our Work section"),
              onComplete: () => console.log("✨ Our Work section fade-out completed"),
            },
            2.8 // Start fade-out after all project transitions complete
          );

        console.log(
          "✅ Our Work content transition animation initialized for desktop"
        );
      } else {
        // For mobile range (300px-500px), ensure both projects are visible without scroll animations
        gsap.set("#project-1", { opacity: 1, zindex: 10 });
        gsap.set("#project-2", { opacity: 1, zIndex: 5 });
        console.log(
          "📱 Mobile layout: Scroll animations disabled, both projects visible"
        );
      }

      // Handle window resize to reinitialize animations
      window.addEventListener("resize", () => {
        const newIsMobileRange =
          window.innerWidth >= 300 && window.innerWidth <= 500;
        console.log(
          `🔄 Resize detected: ${window.innerWidth}px - Mobile range: ${newIsMobileRange}`
        );

        // Just refresh ScrollTrigger instead of reloading the page
        ScrollTrigger.refresh();

        // Update mobile range state without reload
        if (newIsMobileRange !== isMobileRange) {
          // Only update if the mobile range state actually changed
          if (newIsMobileRange) {
            console.log("Mobile range entered - disabling scroll animations");
            // Switching to mobile - ensure both projects are visible
            gsap.set("#project-1", { opacity: 1 });
            gsap.set("#project-2", { opacity: 1 });
          } else {
            // Switching to desktop - reinitialize scroll animations
            gsap.set("#project-2", { opacity: 0 });
          }
        }
      });
    }
  });

  // Viewport Animation for Mobile Floating Images
  document.addEventListener('DOMContentLoaded', function() {
    const leftImage = document.querySelector('.mob-floating-left');
    const rightImage = document.querySelector('.mob-floating-right');
    
    // Create intersection observer to detect when images enter viewport
    const observerOptions = {
      threshold: 0.3, // Trigger when 30% of the element is visible
      rootMargin: '0px 0px -50px 0px' // Trigger slightly before element is fully in view
    };
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Add animation class when element enters viewport
          if (entry.target.classList.contains('mob-floating-left')) {
            setTimeout(() => {
              entry.target.classList.add('animate-in');
            }, 200); // Small delay for left image
          }
          
          if (entry.target.classList.contains('mob-floating-right')) {
            setTimeout(() => {
              entry.target.classList.add('animate-in');
            }, 400); // Slightly longer delay for right image to create staggered effect
          }
          
          // Stop observing once animation is triggered
          observer.unobserve(entry.target);
        }
      });
    }, observerOptions);
    
    // Start observing the mobile floating images
    if (leftImage) {
      observer.observe(leftImage);
    }
    
    if (rightImage) {
      observer.observe(rightImage);
    }
  });



</script>


    <!--====== Our Work Section start ======-->
    <!-- <section class="section-gap">
        <div class="container">
            <div class="row align-items-center justify-content-center">
                <div class="col-lg-12 col-md-12">
                    <div class="title-big">
                      <h2> Blogs </h2>
                    </div>
                </div>
            </div>

            
        </div>
 
        <div class="container-fluid">
         <div class="row align-items-center justify-content-center">
                <div class="col-lg-12 col-md-12 mt-5 p-0">
                    <div class="slick-wrap">
        

                    <div class="slick grid mcs-horizontal">
                      <div class="element-item">
                        <div class="blog-items">
                         <div class="blog-img"> <img src="assets/img/post-1.png" alt="" class="img-fluid"> </div>
                         <div class="blog-content">
                            <h4> The movie in the production </h4>
                            <p> The movie in the production and will come on big screen soon. The movie in the production and will come on big screen soon..... </p>
                            <span class="post-mata"> 24 July 2025 </span>
                         </div>
                        </div>
                      </div>
                      <div class="element-item">
                        <div class="blog-items">
                         <div class="blog-img"> <img src="assets/img/post-2.png" alt="" class="img-fluid"> </div>
                         <div class="blog-content">
                            <h4> The movie in the production </h4>
                            <p> The movie in the production and will come on big screen soon. The movie in the production and will come on big screen soon..... </p>
                            <span class="post-mata"> 24 July 2025 </span>
                         </div>
                        </div>
                      </div>
                      <div class="element-item">
                        <div class="blog-items">
                         <div class="blog-img"> <img src="assets/img/post-3.png" alt="" class="img-fluid"> </div>
                         <div class="blog-content">
                            <h4> The movie in the production </h4>
                            <p> The movie in the production and will come on big screen soon. The movie in the production and will come on big screen soon..... </p>
                            <span class="post-mata"> 24 July 2025 </span>
                         </div>
                        </div>
                      </div>
                      <div class="element-item">
                        <div class="blog-items">
                         <div class="blog-img"> <img src="assets/img/post-1.png" alt="" class="img-fluid"> </div>
                         <div class="blog-content">
                            <h4> The movie in the production </h4>
                            <p> The movie in the production and will come on big screen soon. The movie in the production and will come on big screen soon..... </p>
                            <span class="post-mata"> 24 July 2025 </span>
                         </div>
                        </div>
                      </div>
                      <div class="element-item">
                        <div class="blog-items">
                         <div class="blog-img"> <img src="assets/img/post-2.png" alt="" class="img-fluid"> </div>
                         <div class="blog-content">
                            <h4> The movie in the production </h4>
                            <p> The movie in the production and will come on big screen soon. The movie in the production and will come on big screen soon..... </p>
                            <span class="post-mata"> 24 July 2025 </span>
                         </div>
                        </div>
                      </div>
                      <div class="element-item">
                        <div class="blog-items">
                         <div class="blog-img"> <img src="assets/img/post-3.png" alt="" class="img-fluid"> </div>
                         <div class="blog-content">
                            <h4> The movie in the production </h4>
                            <p> The movie in the production and will come on big screen soon. The movie in the production and will come on big screen soon..... </p>
                            <span class="post-mata"> 24 July 2025 </span>
                         </div>
                        </div>
                      </div>
                      <div class="element-item">
                        <div class="blog-items">
                         <div class="blog-img"> <img src="assets/img/post-1.png" alt="" class="img-fluid"> </div>
                         <div class="blog-content">
                            <h4> The movie in the production </h4>
                            <p> The movie in the production and will come on big screen soon. The movie in the production and will come on big screen soon..... </p>
                            <span class="post-mata"> 24 July 2025 </span>
                         </div>
                        </div>
                      </div>
                      <div class="element-item">
                        <div class="blog-items">
                         <div class="blog-img"> <img src="assets/img/post-2.png" alt="" class="img-fluid"> </div>
                         <div class="blog-content">
                            <h4> The movie in the production </h4>
                            <p> The movie in the production and will come on big screen soon. The movie in the production and will come on big screen soon..... </p>
                            <span class="post-mata"> 24 July 2025 </span>
                         </div>
                        </div>
                      </div>
                      <div class="element-item">
                        <div class="blog-items">
                         <div class="blog-img"> <img src="assets/img/post-3.png" alt="" class="img-fluid"> </div>
                         <div class="blog-content">
                            <h4> The movie in the production </h4>
                            <p> The movie in the production and will come on big screen soon. The movie in the production and will come on big screen soon..... </p>
                            <span class="post-mata"> 24 July 2025 </span>
                         </div>
                        </div>
                      </div>
                      <div class="element-item">
                        <div class="blog-items">
                         <div class="blog-img"> <img src="assets/img/post-1.png" alt="" class="img-fluid"> </div>
                         <div class="blog-content">
                            <h4> The movie in the production </h4>
                            <p> The movie in the production and will come on big screen soon. The movie in the production and will come on big screen soon..... </p>
                            <span class="post-mata"> 24 July 2025 </span>
                         </div>
                        </div>
                      </div>
                      <div class="element-item">
                        <div class="blog-items">
                         <div class="blog-img"> <img src="assets/img/post-2.png" alt="" class="img-fluid"> </div>
                         <div class="blog-content">
                            <h4> The movie in the production </h4>
                            <p> The movie in the production and will come on big screen soon. The movie in the production and will come on big screen soon..... </p>
                            <span class="post-mata"> 24 July 2025 </span>
                         </div>
                        </div>
                      </div>
                      <div class="element-item">
                        <div class="blog-items">
                         <div class="blog-img"> <img src="assets/img/post-3.png" alt="" class="img-fluid"> </div>
                         <div class="blog-content">
                            <h4> The movie in the production </h4>
                            <p> The movie in the production and will come on big screen soon. The movie in the production and will come on big screen soon..... </p>
                            <span class="post-mata"> 24 July 2025 </span>
                         </div>
                        </div>
                      </div>
                    </div>
                  </div>

                </div>
            </div>
          </div>
    </section> -->
    <!--====== Our Work Section end ======-->

    <!-- Horizontal Scrolling Blog Cards Section -->
    <section class="blog-carousel-section">
      <div class="blog-sticky-container">
        <div class="blog-section-title">
          <div class="title-big">
            <h2> Blogs </h2>
          </div>
        </div>
        <div class="blog-cards-wrapper" id="blog-cards-wrapper">
          <div class="blog-card">
            <div class="blog-card-image">
              <img src="assets/img/post-1.png" alt="Blog Post" />
            </div>
            <div class="blog-card-content">
              <h4>The movie in the production</h4>
              <p>The movie in the production and will come on big screen soon. The movie in the production and will come on big screen soon.....</p>
              <span class="post-date">24 July 2025</span>
            </div>
          </div>

          <div class="blog-card">
            <div class="blog-card-image">
              <img src="assets/img/post-2.png" alt="Blog Post" />
            </div>
            <div class="blog-card-content">
              <h4>Behind the Scenes</h4>
              <p>Exclusive behind the scenes footage from our latest production. Get an inside look at the creative process.....</p>
              <span class="post-date">22 July 2025</span>
            </div>
          </div>

          <div class="blog-card">
            <div class="blog-card-image">
              <img src="assets/img/post-3.png" alt="Blog Post" />
            </div>
            <div class="blog-card-content">
              <h4>Director's Vision</h4>
              <p>Our director shares insights into the creative vision and storytelling approach for the upcoming film.....</p>
              <span class="post-date">20 July 2025</span>
            </div>
          </div>

          <div class="blog-card">
            <div class="blog-card-image">
              <img src="assets/img/post-1.png" alt="Blog Post" />
            </div>
            <div class="blog-card-content">
              <h4>Cast Interviews</h4>
              <p>Exclusive interviews with the talented cast members discussing their roles and experiences on set.....</p>
              <span class="post-date">18 July 2025</span>
            </div>
          </div>

          <div class="blog-card">
            <div class="blog-card-image">
              <img src="assets/img/post-2.png" alt="Blog Post" />
            </div>
            <div class="blog-card-content">
              <h4>Production Updates</h4>
              <p>Latest updates from the production team including filming locations and upcoming milestones.....</p>
              <span class="post-date">16 July 2025</span>
            </div>
          </div>

          <div class="blog-card">
            <div class="blog-card-image">
              <img src="assets/img/post-3.png" alt="Blog Post" />
            </div>
            <div class="blog-card-content">
              <h4>Creative Process</h4>
              <p>Deep dive into the creative process and artistic decisions that shape our storytelling approach.....</p>
              <span class="post-date">14 July 2025</span>
            </div>
          </div>

          <div class="blog-card">
            <div class="blog-card-image">
              <img src="assets/img/post-1.png" alt="Blog Post" />
            </div>
            <div class="blog-card-content">
              <h4>On Set Stories</h4>
              <p>Memorable moments and funny stories from the film set that showcase the camaraderie of our team.....</p>
              <span class="post-date">12 July 2025</span>
            </div>
          </div>

          <div class="blog-card">
            <div class="blog-card-image">
              <img src="assets/img/post-2.png" alt="Blog Post" />
            </div>
            <div class="blog-card-content">
              <h4>Technical Insights</h4>
              <p>Technical aspects of film production including cinematography techniques and equipment used.....</p>
              <span class="post-date">10 July 2025</span>
            </div>
          </div>

          <div class="blog-card">
            <div class="blog-card-image">
              <img src="assets/img/post-3.png" alt="Blog Post" />
            </div>
            <div class="blog-card-content">
              <h4>Industry Insights</h4>
              <p>Insights into the film industry and how Electric Marshmallow Productions is making its mark.....</p>
              <span class="post-date">08 July 2025</span>
            </div>
          </div>
        </div>
      </div>
    </section>
<!-- partial -->
  <script src='https://code.jquery.com/jquery-1.11.0.min.js'></script>

    <!--====== Our Work Section start ======-->
    <footer class="footer-section">
      <div class="footer-top">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-4 col-md-4">
                  <div class="footer-logo">
                    <a href="#"> <img src="assets/img/logo.png" alt="" class="img-fluid"> </a>
                  </div>
                </div>
                <div class="col-lg-3 col-md-3">
                  <div class="footer-link">
                    <ul>
                      <li> <a href="#"> Our Work </a> </li>
                      <li> <a href="#"> About us </a> </li>
                    </ul>
                  </div>
                </div>
                <div class="col-lg-3 col-md-3">
                  <div class="footer-link">
                    <ul>
                      <li> <a href="#"> Mission & Values </a> </li>
                      <li> <a href="#"> inclusivity & Impact </a> </li>
                    </ul>
                  </div>
                </div>
                <div class="col-lg-2 col-md-2">
                  <div class="footer-socail">
                    <h5> Contact Us </h5>
                    <ul>
                     <li> <a href="#"> <img src="assets/img/instagram.png"> </a> </li>
                     <li> <a href="#"> <img src="assets/img/vain.png"> </a> </li>
                     <li> <a href="#"> <img src="assets/img/mail.png"> </a> </li>
                    </ul>
                  </div>
                </div>
            </div>
        </div>
      </div>
      <div class="footer-bottom">
        <div class="container">
         <div class="row align-items-center justify-content-center">
          <div class="col-lg-12 col-md-12 text-center">
            <div class="copyright-text"> <p> COPYRIGHT ©2025 </p> </div>
          </div>
         </div>
        </div>
      </div>
    </footer>
    <!--====== Our Work Section end ======-->

    
    <script src="assets/js/vendor/modernizr-3.6.0.min.js"></script>
    <script src="assets/js/vendor/jquery-1.12.4.min.js"></script>
    <!--====== Bootstrap js ======-->
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/popper.min.js"></script>
    
    <script src='https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.5.9/slick.min.js'></script>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/malihu-custom-scrollbar-plugin/3.1.5/jquery.mCustomScrollbar.min.js'></script>
     <script src='https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js'></script> 
    <!--====== Wow JS ======-->
    <script src="assets/js/wow.min.js"></script>
    <!--====== Main js ======-->
    <script src="assets/js/main.js"></script>



<script type="text/javascript">
// Navbar functionality with scroll-lock
class NavbarController {
  constructor() {
    this.isOpen = false;
    this.overlay = document.getElementById("myNav");
    this.hamburgerMenu = document.querySelector(".hamberger-menu");
    this.closeBtn = document.querySelector(".closebtn");
    this.body = document.body;
    this.html = document.documentElement;

    // Store original overflow values
    this.originalBodyOverflow = '';
    this.originalHtmlOverflow = '';

    this.init();
  }

  init() {
    // Add click event listeners
    if (this.hamburgerMenu) {
      this.hamburgerMenu.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggle();
      });
    }

    if (this.closeBtn) {
      this.closeBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.close();
      });
    }

    // Handle window resize
    window.addEventListener('resize', () => {
      // If window is resized to desktop size while menu is open, close it
      if (window.innerWidth > 768 && this.isOpen) {
        this.close();
      }
    });

    // Handle page visibility change (when user navigates away)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.isOpen) {
        this.close();
      }
    });

    // Handle page unload (when user navigates away)
    window.addEventListener('beforeunload', () => {
      if (this.isOpen) {
        this.close();
      }
    });

    // Close menu when clicking outside (on overlay background)
    if (this.overlay) {
      this.overlay.addEventListener('click', (e) => {
        if (e.target === this.overlay) {
          this.close();
        }
      });
    }

    // Handle escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.close();
      }
    });
  }

  toggle() {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  open() {
    if (this.isOpen) return;

    this.isOpen = true;

    // Store current scroll position
    this.scrollPosition = window.scrollY;

    // Store original overflow values
    this.originalBodyOverflow = this.body.style.overflow;
    this.originalHtmlOverflow = this.html.style.overflow;

    // Disable scrolling
    this.body.style.overflow = 'hidden';
    this.html.style.overflow = 'hidden';

    // Prevent touch scrolling on mobile - use scroll position for smoother restoration
    this.body.style.position = 'fixed';
    this.body.style.width = '100%';
    this.body.style.top = `-${this.scrollPosition}px`;

    // Add scroll-locked class for additional CSS control
    this.body.classList.add('scroll-locked');
    this.html.classList.add('scroll-locked');

    // Open the overlay
    if (this.overlay) {
      this.overlay.style.height = "100%";
    }

    // Add visual feedback to hamburger menu
    if (this.hamburgerMenu) {
      this.hamburgerMenu.classList.add('menu-open');
    }

    console.log('Navbar opened - scrolling disabled');
  }

  close() {
    if (!this.isOpen) return;

    this.isOpen = false;

    // Remove scroll-locked classes
    this.body.classList.remove('scroll-locked');
    this.html.classList.remove('scroll-locked');

    // Restore scrolling
    this.body.style.overflow = this.originalBodyOverflow;
    this.html.style.overflow = this.originalHtmlOverflow;

    // Restore body position
    this.body.style.position = '';
    this.body.style.width = '';
    this.body.style.top = '';

    // Restore scroll position smoothly
    if (this.scrollPosition !== undefined) {
      window.scrollTo(0, this.scrollPosition);
    }

    // Close the overlay
    if (this.overlay) {
      this.overlay.style.height = "0%";
    }

    // Remove visual feedback from hamburger menu
    if (this.hamburgerMenu) {
      this.hamburgerMenu.classList.remove('menu-open');
    }

    console.log('Navbar closed - scrolling restored');
  }
}

// Legacy function support (for existing onclick handlers)
let navbarController;

function openNav() {
  if (!navbarController) {
    navbarController = new NavbarController();
  }
  navbarController.open();
}

function closeNav() {
  if (!navbarController) {
    navbarController = new NavbarController();
  }
  navbarController.close();
}

// Initialize navbar controller when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  navbarController = new NavbarController();
});
</script>

<style>
/* Popup Animation Keyframes */
@keyframes popupSlideUp {
  0% {
    opacity: 0;
    transform: translateY(var(--start-y, 30px)) scale(var(--start-scale, 0.9));
  }
  60% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Mobile Image Slide Animations */
@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-100px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(100px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Initial state for mobile floating images - hidden until animated */
.mob-floating-left, .mob-floating-right {
  opacity: 0;
  transform: translateX(-100px);
}

.mob-floating-right {
  transform: translateX(100px);
}

/* Animation classes to be added when in viewport */
.mob-floating-left.animate-in {
  animation: slideInLeft 1s ease-out forwards;
}

.mob-floating-right.animate-in {
  animation: slideInRight 1s ease-out forwards;
}

/* Our Work Content Transition Styles */
.work-transition-section {
  height: 100vh; /* SIMPLIFIED: Use natural height for better compatibility */
  background-color: #000;
  position: relative;
  overflow: hidden;
}

.work-sticky-container {
  position: sticky; /* RESTORED: Match working b2.html exactly */
  top: 0; /* RESTORED: Match working b2.html exactly */
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #000;
}

.work-section-title {
  position: absolute;
  top: 5%;
  /* left: 2rem; */
  z-index: 10;
  width: 100%;
  pointer-events: none;
}

.work-section-title .title-big h2 {
  color: #fff;
  font-size: 4rem;
  text-align: left;
  margin-bottom: 0;
}

.work-title-line {
  width: 120px;
  height: 4px;
  background: linear-gradient(90deg, #fff 0%, #888 100%);
  margin-top: 1rem;
  border-radius: 2px;
}

.work-content-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  padding-top: 20%;
  align-items: center;
  justify-content: center;
}

.work-project {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  will-change: opacity;
}

.work-content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  max-width: 1200px;
  width: 100%;
  padding: 0 2rem;
}

.work-content-left {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.project-title {
  font-size: 4rem;
  color: #fff;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.project-credits {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.project-credits li {
  color: #fff;
  font-size: 1.1rem;
}

.project-credits .text-grey {
  color: #888;
  margin-right: 0.5rem;
}

.project-btn-wrap {
  margin-top: 1rem;
}

.project-btn {
  display: inline-flex;
  align-items: center;
  padding: 1rem 2rem;
  background-color: transparent;
  border: 2px solid #fff;
  color: #fff;
  text-decoration: none;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border-radius: 50px;
}

.project-btn:hover {
  background-color: #fff;
  color: #000;
  transform: translateY(-2px);
}

.work-content-right {
  display: flex;
  justify-content: center;
  align-items: center;
}

.project-image-container {
  position: relative;
  width: 100%;
  max-width: 500px;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
}

/* ENHANCED HOVER EFFECTS - CONSISTENT FOR BOTH PROJECTS */
.project-image-container:hover {
  transform: scale(1.05) !important;
}

/* Ensure hover effects work even during GSAP opacity animations */
#project-1 .project-image-container:hover,
#project-2 .project-image-container:hover {
  transform: scale(1.05) !important;
  transition: transform 0.3s ease !important;
}
#project-1 {
  z-index: 20;
}

#project-2{
  z-index: 10;
}

.project-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.3s ease;
}

/* Enhanced image hover effect */
.project-image-container:hover .project-image {
  transform: scale(1.02);
}

.view-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.9);
  color: #000;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  font-weight: 700;
  font-size: 0.9rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 10; /* Ensure overlay appears above image */
}

/* Ensure overlay hover works for both projects */
.project-image-container:hover .view-overlay,
#project-1 .project-image-container:hover .view-overlay,
#project-2 .project-image-container:hover .view-overlay {
  opacity: 1 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .work-content-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .project-title {
    font-size: 2.5rem;
  }
  
  .work-section-title .title-big h2 {
    font-size: 2.5rem;
  }
  
  .work-section-title {
    top: 5%;
    left: 1rem;
  }
  
  .work-title-line {
    width: 80px;
    height: 3px;
  }
  
  .work-transition-section {
    height: 150vh;
  }

  /* MOBILE HOVER EFFECTS - Touch-friendly */
  .project-image-container:active,
  .project-image-container:focus {
    transform: scale(1.05);
  }

  .project-image-container:active .view-overlay,
  .project-image-container:focus .view-overlay,
  #project-1 .project-image-container:active .view-overlay,
  #project-2 .project-image-container:active .view-overlay {
    opacity: 1 !important;
  }

  /* Ensure mobile touch interactions work */
  .project-image-container {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }
}

@media (max-width: 500px) and (min-width: 300px) {
  /* Remove scroll-based animations for mobile */
  .work-transition-section {
    height: auto !important; /* Remove fixed height */
    position: relative !important;
    overflow: visible !important;
  }
  
  .work-sticky-container {
    position: relative !important; /* Remove sticky positioning */
    height: auto !important;
    display: block !important;
    overflow: visible !important;
  }
  
  .work-content-container {
    position: relative !important;
    height: auto !important;
    display: block !important;
    padding: 2rem 0 !important;
  }
  
  /* Arrange projects vertically */
  .work-project {
    position: relative !important; /* Remove absolute positioning */
    opacity: 1 !important; /* Always visible */
    display: block !important;
    margin-bottom: 3rem;
    padding: 1.5rem 0;
  }
  
  /* Vertical layout for work content between 300px-500px */
  .work-content-grid {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: left; /* Changed to left alignment */
    gap: 1.5rem;
    max-width: 400px;
    margin: 0 auto;
    padding: 0 1rem;
  }
  
  /* Image first with popup animation */
  .work-content-right {
    order: 1;
    width: 100%;
    animation: popupSlideUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  
  /* Content below image */
  .work-content-left {
    order: 2;
    width: 100%;
    gap: 1rem;
    text-align: left; /* Left alignment for content */
    align-items: flex-start; /* Align items to the left */
  }
  
  /* Title with popup animation and custom font */
  .project-title {
    font-size: 2.2rem;
    margin-bottom: 0.8rem;
    animation: popupSlideUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0.2s forwards;
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    text-align: left !important; /* Force left alignment */
    font-family: url('assets/fonts/fa-regular-400.woff2') format('woff2'), sans-serif;
  }
  
  /* Credits with staggered popup animation and custom font */
  .project-credits {
    animation: popupSlideUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0.4s forwards;
    opacity: 0;
    transform: translateY(15px) scale(0.98);
    text-align: left !important; /* Force left alignment */
    align-items: flex-start !important;
  }
  
  .project-credits li {
    font-size: 1rem;
    text-align: left !important; /* Force left alignment */
    font-family: url('assets/fonts/fa-regular-400.woff2') format('woff2'), sans-serif;
  }
  
  /* Button with popup animation and custom font */
  .project-btn-wrap {
    margin-top: 1.2rem;
    animation: popupSlideUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0.6s forwards;
    opacity: 0;
    transform: translateY(10px) scale(0.99);
    text-align: left !important; /* Force left alignment */
    align-self: flex-start; /* Align button to the left */
  }
  
  .project-btn {
    font-size: 1rem;
    padding: 0.8rem 1.5rem;
    font-family: url('assets/fonts/fa-regular-400.woff2') format('woff2'), sans-serif;
  }
  
  /* Image container adjustments */
  .project-image-container {
    max-width: 350px;
    width: 100%;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  }
  
  /* Staggered animations for different projects - BALANCED FOR BOTH PROJECTS */

  /* Project 1 animations */
  #project-1 .work-content-right {
    animation-delay: 0.2s;
  }

  #project-1 .project-title {
    animation-delay: 0.4s;
  }

  #project-1 .project-credits {
    animation-delay: 0.6s;
  }

  #project-1 .project-btn-wrap {
    animation-delay: 0.8s;
  }

  /* Project 2 animations */
  #project-2 .work-content-right {
    animation-delay: 0.3s;
  }

  #project-2 .project-title {
    animation-delay: 0.5s;
  }

  #project-2 .project-credits {
    animation-delay: 0.7s;
  }

  #project-2 .project-btn-wrap {
    animation-delay: 0.9s;
  }

  /* ENSURE HOVER EFFECTS WORK DURING GSAP ANIMATIONS */
  /* High specificity rules to override GSAP inline styles */
  .work-project .project-image-container:hover {
    transform: scale(1.05) !important;
  }

  .work-project .project-image-container:hover .project-image {
    transform: scale(1.02) !important;
  }

  .work-project .project-image-container:hover .view-overlay {
    opacity: 1 !important;
  }
  
  /* Section title adjustments */
  .work-section-title {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    margin-bottom: 2rem;
    text-align: center;
  }
  
  .work-section-title .title-big h2 {
    font-size: 2.2rem;
    animation: popupSlideUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    font-family: url('assets/fonts/fa-regular-400.woff2') format('woff2'), sans-serif;
  }
}

@media (max-width: 480px) {
  .project-title {
    font-size: 2rem;
  }
  
  .work-section-title .title-big h2 {
    font-size: 2rem;
  }
  
  .work-content-grid {
    padding: 0 1rem;
  }
  
  .work-title-line {
    width: 60px;
    height: 2px;
  }
  
  .work-transition-section {
    height: 120vh;
  }
}

/* Blog Horizontal Scroll Styles */
.blog-carousel-section {
  height: 400vh; /* Increased height for much more scroll area */
  /* background-color: #000; */
  /* background-color: #fff !important; */
  position: relative;
  overflow: hidden;
}

.blog-sticky-container {
  position: sticky;
  top: 0;
  height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  /* background-color: #000 ; */
  background:none;
}

.blog-section-title {
  position: absolute;
  top: 10%;
  left: 2rem;
  z-index: 10;
  pointer-events: none;
}

.blog-section-title .title-big h2 {
  color: #fff;
  font-size: 4rem;
  text-align: left;
  margin-bottom: 3rem;
}

.blog-cards-wrapper {
  display: flex;
  gap: 2rem;
  will-change: transform;
  padding: 0 2rem 0 2rem;
  align-items: center;
  height: 100%;
  padding-top: 140px;
  padding-right: 50vw; /* Add extra padding to ensure last card is fully visible */
}

.blog-card {
  height: 450px;
  width: 400px;
  background-color: #1a1a1a;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid #333;
}

.blog-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(255, 255, 255, 0.1);
}

.blog-card-image {
  height: 250px;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.blog-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.blog-card:hover .blog-card-image img {
  transform: scale(1.1);
}

.blog-card-content {
  padding: 1.5rem;
  color: white;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.blog-card-content h4 {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: #fff;
  line-height: 1.4;
}

.blog-card-content p {
  font-size: 0.9rem;
  color: #ccc;
  line-height: 1.5;
  margin-bottom: 1rem;
  flex-grow: 1;
}

.blog-card-content .post-date {
  font-size: 0.8rem;
  color: #888;
  font-weight: 500;
  margin-top: auto;
}

/* Footer spacing fix */
.footer-section {
  position: relative;
  z-index: 100;
  background-color: #000;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .blog-card {
    width: 320px;
    height: 400px;
  }
  
  .blog-card-image {
    height: 200px;
  }
  
  .blog-card-content {
    height: 200px;
    padding: 1rem;
  }
  
  .blog-cards-wrapper {
    padding: 0 1rem;
    gap: 1.5rem;
  }
  
  .blog-carousel-section {
    height: 600vh; /* Significantly increased height for mobile - from 300vh to 600vh */
  }
  
  .blog-section-title .title-big h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
  }
  
  .blog-section-title {
    top: 8%;
  }
}

@media (max-width: 480px) {
  .blog-card {
    width: 280px;
    height: 380px;
  }
  .animation-spacer{
    display: none;
  }
  
  .blog-card-image {
    height: 180px;
  }
  
  .blog-cards-wrapper {
    gap: 1rem;
  }
  
  .blog-carousel-section {
    height: 900vh; /* Increased scroll area for very small screens - from 800vh to 900vh */
  }
  
  .blog-section-title .title-big h2 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }
  
  .blog-section-title {
    top: 6%;
  }
}
@media screen and (min-width: 769px) {
  .blog-carousel-section{
    /* margin-top: 50px; */
  }
}
</style>

<script type="text/javascript">
// Blog Horizontal Scroll with GSAP
document.addEventListener('DOMContentLoaded', function() {
  // Wait for GSAP to be fully loaded
  setTimeout(() => {
    try {
      console.log("🔍 Setting up blog horizontal scroll...");

      // Check if GSAP and ScrollTrigger are available
      if (typeof gsap === 'undefined') {
        console.error("❌ GSAP is not loaded!");
        return;
      }
      if (typeof ScrollTrigger === 'undefined') {
        console.error("❌ ScrollTrigger is not loaded!");
        return;
      }

      const blogSection = document.querySelector('.blog-carousel-section');
      const wrapper = document.getElementById('blog-cards-wrapper');

      if (blogSection && wrapper) {
        console.log(`✅ Found blog section and wrapper`);

        // Calculate total scroll distance
        const getScrollDistance = () => {
          const wrapperWidth = wrapper.scrollWidth;
          const viewportWidth = window.innerWidth;
          
          // Use actual scrollWidth but add extra buffer to ensure all cards are visible
          const extraBuffer = 200; // Extra pixels to ensure last card is fully visible
          const distance = wrapperWidth - viewportWidth + extraBuffer;
          
          console.log(`📐 Enhanced scroll calculation:
            - Wrapper scrollWidth: ${wrapperWidth}px
            - Viewport width: ${viewportWidth}px
            - Extra buffer: ${extraBuffer}px
            - Total scroll distance: ${distance}px`);
          
          return Math.max(distance, 0);
        };

        // Create horizontal scroll animation with optimized settings
        const totalScrollLength = getScrollDistance();
        
        gsap.to(wrapper, {
          x: () => `-${totalScrollLength}px`,
          ease: "none",
          scrollTrigger: {
            trigger: blogSection,
            start: "top top",
            end: () => `+=${blogSection.offsetHeight}`, // Use full section height for complete scroll
            scrub: 1,
            pin: true,
            anticipatePin: 1,
            invalidateOnRefresh: true,
            pinSpacing: false, // Prevents extra spacing after animation
            onStart: () => {
              console.log("🚀 Blog horizontal scroll started");
            },
            onUpdate: (self) => {
              const progress = Math.round(self.progress * 100);
              if (progress % 20 === 0) {
                console.log(`📊 Scroll progress: ${progress}%`);
              }
            },
            onComplete: () => {
              console.log("✅ Blog horizontal scroll completed");
            },
            onLeave: () => {
              // Ensure smooth transition to footer
              console.log("🏁 Leaving blog section, transitioning to footer");
            }
          }
        });

        // Add entrance animation for cards
        gsap.fromTo('.blog-card', 
          {
            opacity: 0,
            y: 50
          },
          {
            opacity: 1,
            y: 0,
            duration: 0.8,
            stagger: 0.1,
            ease: "power2.out",
            scrollTrigger: {
              trigger: blogSection,
              start: "top 80%",
              toggleActions: "play none none reverse"
            }
          }
        );

        // Handle resize with debouncing
        let resizeTimer;
        window.addEventListener('resize', () => {
          clearTimeout(resizeTimer);
          resizeTimer = setTimeout(() => {
            console.log("🔄 Refreshing on resize");
            ScrollTrigger.refresh();
          }, 300);
        });

        console.log("✅ Blog horizontal scroll setup complete");

      } else {
        console.error("❌ Blog section elements not found:", {
          blogSection: !!blogSection,
          wrapper: !!wrapper
        });
      }

    } catch (error) {
      console.error("❌ Error in blog horizontal scroll setup:", error);
    }
  }, 1000); // Wait 1 second for all animations to be ready
});
</script>

</script>





</body>

</html>
