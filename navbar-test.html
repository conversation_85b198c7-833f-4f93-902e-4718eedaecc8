<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navbar Scroll Lock Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #000;
            color: #fff;
            line-height: 1.6;
        }
        
        .test-content {
            height: 200vh;
            background: linear-gradient(45deg, #333, #666);
            padding: 20px;
            margin: 20px 0;
        }
        
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(0,0,0,0.9);
            padding: 10px 20px;
            z-index: 100;
        }
        
        .hamburger {
            cursor: pointer;
            padding: 10px;
            background: #333;
            color: #fff;
            border: none;
            border-radius: 4px;
        }
        
        .overlay {
            height: 0%;
            width: 100%;
            position: fixed;
            z-index: 999;
            top: 0;
            right: 0;
            background-color: rgba(0, 0, 0, 0.9);
            overflow-y: hidden;
            transition: height 0.5s ease;
        }
        
        .overlay-content {
            position: relative;
            top: 25%;
            width: 100%;
            text-align: center;
            margin-top: 30px;
        }
        
        .overlay a {
            padding: 8px;
            text-decoration: none;
            font-size: 36px;
            color: #818181;
            display: block;
            transition: 0.3s;
        }
        
        .overlay a:hover {
            color: #f1f1f1;
        }
        
        .closebtn {
            position: absolute;
            top: 20px;
            right: 45px;
            font-size: 60px;
            cursor: pointer;
        }
        
        .scroll-locked {
            overflow: hidden !important;
            position: fixed !important;
            width: 100% !important;
        }
        
        .test-section {
            margin: 50px 0;
            padding: 20px;
            background: #222;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="navbar">
        <button class="hamburger" onclick="openNav()">☰ Open Menu</button>
    </div>
    
    <div id="myNav" class="overlay">
        <a href="javascript:void(0)" class="closebtn" onclick="closeNav()">&times;</a>
        <div class="overlay-content">
            <a href="#">Home</a>
            <a href="#">About</a>
            <a href="#">Services</a>
            <a href="#">Contact</a>
        </div>
    </div>
    
    <div style="margin-top: 80px;">
        <h1>Navbar Scroll Lock Test</h1>
        <p>This page tests the navbar scroll-lock functionality:</p>
        
        <div class="test-section">
            <h2>Test Instructions:</h2>
            <ol>
                <li>Try scrolling this page normally - it should work</li>
                <li>Click the "☰ Open Menu" button</li>
                <li>Try scrolling while the menu is open - scrolling should be disabled</li>
                <li>Close the menu by clicking the "×" button or pressing Escape</li>
                <li>Scrolling should be restored to the same position</li>
                <li>Test on both desktop and mobile devices</li>
            </ol>
        </div>
        
        <div class="test-content">
            <h2>Scrollable Content Section 1</h2>
            <p>This is a tall section to test scrolling behavior. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        </div>
        
        <div class="test-content">
            <h2>Scrollable Content Section 2</h2>
            <p>More content to test scroll behavior. When the navbar is open, this content should not be scrollable. When closed, normal scrolling should resume from the exact same position.</p>
            <p>Test various scenarios: mouse wheel, touch scrolling on mobile, keyboard scrolling, etc.</p>
        </div>
    </div>

    <script>
        // Navbar functionality with scroll-lock (simplified version for testing)
        class NavbarController {
            constructor() {
                this.isOpen = false;
                this.overlay = document.getElementById("myNav");
                this.body = document.body;
                this.html = document.documentElement;
                this.scrollPosition = 0;
                
                this.init();
            }
            
            init() {
                // Handle escape key
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && this.isOpen) {
                        this.close();
                    }
                });
                
                // Handle window resize
                window.addEventListener('resize', () => {
                    if (window.innerWidth > 768 && this.isOpen) {
                        this.close();
                    }
                });
            }
            
            open() {
                if (this.isOpen) return;
                
                this.isOpen = true;
                this.scrollPosition = window.scrollY;
                
                // Disable scrolling
                this.body.style.overflow = 'hidden';
                this.html.style.overflow = 'hidden';
                this.body.style.position = 'fixed';
                this.body.style.width = '100%';
                this.body.style.top = `-${this.scrollPosition}px`;
                
                // Open overlay
                if (this.overlay) {
                    this.overlay.style.height = "100%";
                }
                
                console.log('Menu opened - scrolling disabled');
            }
            
            close() {
                if (!this.isOpen) return;
                
                this.isOpen = false;
                
                // Restore scrolling
                this.body.style.overflow = '';
                this.html.style.overflow = '';
                this.body.style.position = '';
                this.body.style.width = '';
                this.body.style.top = '';
                
                // Restore scroll position
                window.scrollTo(0, this.scrollPosition);
                
                // Close overlay
                if (this.overlay) {
                    this.overlay.style.height = "0%";
                }
                
                console.log('Menu closed - scrolling restored');
            }
        }

        // Initialize navbar controller
        let navbarController;
        document.addEventListener('DOMContentLoaded', function() {
            navbarController = new NavbarController();
        });

        // Legacy functions for onclick handlers
        function openNav() {
            if (!navbarController) {
                navbarController = new NavbarController();
            }
            navbarController.open();
        }

        function closeNav() {
            if (!navbarController) {
                navbarController = new NavbarController();
            }
            navbarController.close();
        }
    </script>
</body>
</html>
