/*=======  COMMON CSS  =======*/

@import url("https://fonts.googleapis.com/css2?family=Big+Shoulders:opsz,wght@10..72,100..900&family=Host+Grotesk:ital,wght@0,300..800;1,300..800&display=swap");

/* font-family: "Big Shoulders", sans-serif; */
/* font-family: "Host Grotesk", sans-serif; */

.kaisei-tokumin-regular {
  font-family: "Kaisei Tokumin", serif;
  font-weight: 400;
  font-style: normal;
}

body {
  font-family: "Host Grotesk", sans-serif;
  font-weight: 300;
  font-style: normal;
  color: #fff;
  overflow-x: hidden;
  font-size: 19px;
  line-height: 1.6;
  background: #000;
}

.mobile-section-wrapper {
  display: none;
}

@media (max-width: 575px) {
  body {
    font-size: 16px;
  }
}

* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

img {
  max-width: 100%;
  -webkit-transition: all 0.4s ease-out 0s;
  transition: all 0.4s ease-out 0s;
}

a:focus,
input:focus,
textarea:focus,
button:focus,
.slick-initialized .slick-slide:focus,
.btn:focus,
select:focus {
  text-decoration: none;
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

input,
textarea,
button,
select {
  border: none;
}

input,
textarea,
select {
  width: 100%;
  background-color: #fff;
  padding: 0 25px;
  height: 55px;
  font-weight: 700;
  font-size: 15px;
  color: #616161;
}

textarea {
  height: 90px;
  padding-top: 20px;
  resize: none;
}

::-webkit-input-placeholder {
  opacity: 1;
}

:-ms-input-placeholder {
  opacity: 1;
}

::-ms-input-placeholder {
  opacity: 1;
}

::placeholder {
  opacity: 1;
}

::-webkit-scrollbar {
  background-color: #ccc;
  width: 8px;
}

::-webkit-scrollbar-thumb {
  background-color: #7531ad61;
}

.input-group {
  position: relative;
}

.input-group input,
.input-group textarea,
.input-group select {
  padding-right: 50px;
}

.input-group select {
  -webkit-appearance: none;
  -moz-appearance: none;
}

.input-group select::-ms-expand {
  display: none;
}

.input-group .icon {
  position: absolute;
  right: 25px;
  font-size: 14px;
  color: #3ab9fe;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.input-group.textarea .icon {
  top: 20px;
  -webkit-transform: translateY(0);
  transform: translateY(0);
}

.slick-slide img {
  display: inline-block;
}

a:focus,
a:hover {
  text-decoration: none;
}

i,
span,
a {
  display: inline-block;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Big Shoulders", sans-serif;
  font-weight: 500;
  margin: 0px;
}

h1,
h1 a,
h2,
h2 a,
h3,
h3 a,
h4,
h4 a,
h5,
h5 a,
h6,
h6 a {
  color: #fff;
}

h1 {
  font-size: 48px;
}

h2 {
  font-size: 36px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 22px;
}

h5 {
  font-size: 18px;
}

h6 {
  font-size: 16px;
}

ul,
ol {
  margin: 0px;
  padding: 0px;
  list-style-type: none;
}

p {
  margin: 0px;
}

a {
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
}

a,
a:hover {
  color: #3ab9fe;
}

.mobile-section-wrapper {
  display: none;
}

/*=======  Common Classes  =======*/
.main-btn {
  line-height: 52px;
  font-size: 15px;
  font-weight: 500;
  border-radius: 1px;
  border: none;
  cursor: pointer;
  padding: 0 30px;
  border: 1px #fff solid;
  /* background-color: #AF875F; */
  color: #fff;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
  position: relative;
  z-index: 2;
  margin-top: 20px;
}
.main-btn:hover {
  color: #fff;
  background-color: #7531ad61;
  border: 1px #7531ad61 solid;
}
.main-btn svg {
  font-size: 22px;
  margin-left: 2px;
  fill: #fff;
  transform: translate(0px, -2px);
  max-width: 16px;
  min-width: 16px;
  vertical-align: middle;
}

.main-btn-4 {
  background: transparent;
  border: 2px #fff solid;
  color: #fff;
  line-height: 52px;
}
.main-btn-4:hover {
  border: 2px #917047 solid;
  color: #fff;
}

.main-content-img {
  padding: 30px 200px;
}

@media (max-width: 1199px) {
  .main-btn {
    padding: 0 40px;
    font-size: 16px;
    line-height: 60px;
  }
  .mobile-section-wrapper {
    display: none;
  }
}

@media (max-width: 575px) {
  .main-btn {
    padding: 0 35px;
    font-size: 14px;
    line-height: 60px;
  }
}

@media (max-width: 1199px) {
  .view-moore-btn .main-btn {
    padding: 0 35px;
  }
  .mobile-section-wrapper {
    display: none;
  }
}

.bg-img-c {
  background-size: cover;
  background-position: center;
  background-color: #979797;
}

.section-title .title-tag {
  font-size: 20px;
  font-weight: 700;
  color: #3ab9fe;
  display: inline-block;
  position: relative;
  margin-bottom: 15px;
}

@media (max-width: 575px) {
  .section-title .title-tag {
    font-size: 18px;
  }
  .mobile-section-wrapper {
    display: none;
  }
}

.section-title .title-tag::before,
.section-title .title-tag::after {
  position: absolute;
  left: 0;
  width: 30px;
  height: 3px;
  background-color: #3ab9fe;
  content: "";
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  display: none;
}

.section-title.left-border .title-tag {
  padding-left: 45px;
}

.section-title.left-border .title-tag::before {
  display: block;
}

.section-title.both-border .title-tag {
  padding: 0 45px;
}

.section-title.both-border .title-tag::before,
.section-title.both-border .title-tag::after {
  display: block;
}

.section-title .title {
  font-size: 45px;
  line-height: 1.2;
  text-transform: uppercase;
}

@media (max-width: 1199px) {
  .section-title .title {
    font-size: 42px;
  }
}

@media (max-width: 767px) {
  .section-title .title {
    font-size: 34px;
  }
  .mobile-section-wrapper {
    display: none;
  }
  .main-btn-4 {
    line-height: 55px;
    margin-top: 15px;
    padding: 0 25px;
    margin-left: 0px;
  }
  .cta-wrap {
    padding: 60px 0 100px !important;
  }
}

@media (max-width: 575px) {
  .section-title .title {
    font-size: 28px;
  }
}

@media (max-width: 399px) {
  .section-title .title {
    font-size: 24px;
  }
}

.section-gap {
  padding-top: 100px;
  padding-bottom: 100px;
}

@media (max-width: 991px) {
  .section-gap {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}

.section-gap-bottom {
  padding-bottom: 100px;
}

@media (max-width: 991px) {
  .section-gap-bottom {
    padding-bottom: 20px;
  }
}

.section-gap-top {
  padding-top: 130px;
}

@media (max-width: 991px) {
  .section-gap-top {
    padding-top: 100px;
  }
}

.grey-bg {
  background-color: #f5f5f5;
}

@media (min-width: 1600px) {
  .container-1600 {
    max-width: 1600px;
  }
}

@media (min-width: 1600px) {
  .container-fluid.p-70 {
    padding-left: 70px;
    padding-right: 70px;
  }
  .mobile-section-wrapper {
    display: none;
  }
}

@media (max-width: 399px) {
  .row .col-tiny-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
}

/*=======  Utilitis =======*/
.mt-30 {
  margin-top: 30px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-60 {
  margin-top: 60px;
}

.mt-80 {
  margin-top: 60px;
}
.mb-10 {
  margin-bottom: 10px;
}
.mb-20 {
  margin-bottom: 20px;
}
.mb-25 {
  margin-bottom: 25px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mb-80 {
  margin-bottom: 80px;
}

/*=======  Animations  =======*/
@-webkit-keyframes sticky {
  0% {
    top: -200px;
  }
  100% {
    top: 0;
  }
}
@keyframes sticky {
  0% {
    top: -200px;
  }
  100% {
    top: 0;
  }
}

@-webkit-keyframes sk-foldCubeAngle {
  0%,
  10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%,
  75% {
    -webkit-transform: perspective(140px) rotateX(0);
    transform: perspective(140px) rotateX(0);
    opacity: 1;
  }
  100%,
  90% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}

@keyframes sk-foldCubeAngle {
  0%,
  10% {
    -webkit-transform: perspective(140px) rotateX(-180deg);
    transform: perspective(140px) rotateX(-180deg);
    opacity: 0;
  }
  25%,
  75% {
    -webkit-transform: perspective(140px) rotateX(0);
    transform: perspective(140px) rotateX(0);
    opacity: 1;
  }
  100%,
  90% {
    -webkit-transform: perspective(140px) rotateY(180deg);
    transform: perspective(140px) rotateY(180deg);
    opacity: 0;
  }
}

/*======= Header =======*/
header .header-nav {
  position: relative;
  background-color: #fff;
}

@media (max-width: 575px) {
  header .header-nav {
    top: 0;
  }
}

header .header-nav .nav-container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

header .header-nav .nav-container .navbar-toggler {
  border: 1px solid #3ab9fe;
  background-color: transparent;
  cursor: pointer;
  display: none;
  padding: 15px;
  margin-left: 30px;
}

header .header-nav .nav-container .navbar-toggler span {
  position: relative;
  background-color: #3ab9fe;
  border-radius: 0;
  display: block;
  height: 3px;
  margin-top: 5px;
  padding: 0;
  -webkit-transition-duration: 300ms;
  transition-duration: 300ms;
  width: 30px;
  cursor: pointer;
  display: block;
}

header .header-nav .nav-container .navbar-toggler span:first-child {
  margin-top: 0;
}

header .header-nav .nav-container .navbar-toggler.active span:nth-of-type(1) {
  -webkit-transform: rotate3d(0, 0, 1, 45deg);
  transform: rotate3d(0, 0, 1, 45deg);
  top: 8px;
}

header .header-nav .nav-container .navbar-toggler.active span:nth-of-type(2) {
  opacity: 0;
}

header .header-nav .nav-container .navbar-toggler.active span:nth-of-type(3) {
  -webkit-transform: rotate3d(0, 0, 1, -45deg);
  transform: rotate3d(0, 0, 1, -45deg);
  top: -8px;
}

header .header-nav .nav-container .navbar-close {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 12;
  display: none;
}

header .header-nav .nav-container .navbar-close .cross-wrap {
  width: 26px;
  height: 26px;
  cursor: pointer;
  position: relative;
}

header .header-nav .nav-container .navbar-close .cross-wrap span {
  position: absolute;
  display: block;
  width: 100%;
  height: 2px;
  border-radius: 6px;
  background: #fff;
}

header .header-nav .nav-container .navbar-close .cross-wrap span:first-child {
  top: 12px;
  left: 0;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

header .header-nav .nav-container .navbar-close .cross-wrap span:last-child {
  bottom: 12px;
  left: 0;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

header .header-nav .nav-container .menu-items {
  position: relative;
}

header .header-nav .nav-container .menu-items ul li {
  display: inline-block;
}

header .header-nav .nav-container .menu-items ul li.has-submemu {
  position: relative;
}

header .header-nav .nav-container .menu-items ul li.has-submemu::after {
  font-family: "Font Awesome 5 Pro";
  font-weight: 400;
  content: "\f107";
  position: absolute;
  right: 5px;
  top: 51%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

@media (max-width: 1599px) {
  header .header-nav .nav-container .menu-items ul li.has-submemu::after {
    right: -4px;
  }
}

header .header-nav .nav-container .menu-items ul li a {
  color: #14212b;
  padding: 0 15px;
  margin: 0 15px;
  line-height: 100px;
  position: relative;
  font-weight: 500;
  font-family: "Barlow Condensed", sans-serif;
}

@media (max-width: 1599px) {
  header .header-nav .nav-container .menu-items ul li a {
    padding: 0 10px;
    margin: 0 5px;
  }
  .mobile-section-wrapper {
    display: none;
  }
}

@media (max-width: 1199px) {
  header .header-nav .nav-container .menu-items ul li a {
    margin: 0 5px;
    padding: 0 6px;
    font-size: 16px;
  }
}

header .header-nav .nav-container .menu-items ul li a:hover {
  color: #3ab9fe;
}

header .header-nav .nav-container .menu-items ul li .submenu {
  position: absolute;
  left: 0;
  top: 110%;
  width: 200px;
  background-color: #fff;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
  z-index: 99;
  height: auto;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

header .header-nav .nav-container .menu-items ul li .submenu li {
  display: block;
}

header .header-nav .nav-container .menu-items ul li .submenu li a {
  display: block;
  padding: 8px 30px;
  position: relative;
  -webkit-transition: all 0.3s ease-out 0s;
  transition: all 0.3s ease-out 0s;
  border-radius: 0;
  margin: 0 0;
  line-height: 30px !important;
  color: #616161;
}

header .header-nav .nav-container .menu-items ul li .submenu li a:hover {
  background-color: #3ab9fe;
  color: #fff !important;
}

header .header-nav .nav-container .menu-items ul li .submenu li .submenu {
  left: 100%;
  top: 50%;
}

header .header-nav .nav-container .menu-items ul li .submenu li:hover .submenu {
  top: 0;
}

header .header-nav .nav-container .menu-items ul li:hover > .submenu {
  opacity: 1;
  visibility: visible;
  top: 100%;
}

header .header-nav .nav-container .menu-items ul li .dd-trigger {
  display: none;
}

header .header-nav .nav-container .offcanvas-toggler {
  background-color: #3ab9fe;
  cursor: pointer;
  border-radius: 5px;
  height: 65px;
  width: 65px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.site-logo {
  padding: 15px 0;
  max-width: 170px;
  display: flex;
  align-items: center;
  column-gap: 25px;
}

header.header-three {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: auto;
  z-index: 1000; /* ensure header above hero */
  border-bottom: 1px #ffffff20 solid;
}

@media (min-width: 1800px) {
  header.header-three .container-fluid {
    max-width: 1780px;
  }
  .mobile-section-wrapper {
    display: none;
  }
}

header.header-three .header-nav {
  background-color: transparent;
}
.quize-btn-header {
  position: absolute;
  right: 0;
}
/*.header-right-icon ul li {
    display: inline-block;
    max-width: 30px;
    margin: 0 7px;
}*/
.header-right-icon ul li {
  display: inline-block;
  transition: transform 0.3s ease, filter 0.3s ease;
  color: white;
  font-size: 24px;
  position: relative;
  max-width: 30px;
  margin: 0 7px;
}

/* Hover Effect */
.header-right-icon ul li:hover {
  transform: translateY(-6px) scale(1.1);
  filter: drop-shadow(0 0 8px #00ffe1); /* Glow */
}

/*======= left folting menu =======*/

.hamberger-menu {
  max-width: 28px;
  cursor: pointer;
  min-width: 28px;
}
/*.overlay {
  height: 100%;
  width: 0;
  position: fixed;
  z-index: 1111;
  top: 0;
  right: 0;
  background-color: rgb(0,0,0);
  background-color: rgba(0,0,0, 0.9);
  overflow-x: hidden;
  transition: 0.5s;
}
*/
.overlay {
  height: 0%;
  width: 100%;
  position: fixed;
  z-index: 999;
  top: 0;
  left: 0;
  background-color: rgb(0, 0, 0);
  background-color: rgba(0, 0, 0, 0.9);
  overflow-y: hidden;
  transition: 0.5s;
}

.overlay-content {
  position: relative;
  top: 0%;
  width: 100%;
  text-align: left;
  margin-top: 30px;
  display: flex;
  align-items: center;
}

.overlay a {
  padding: 0 15px;
  text-decoration: none;
  font-size: 36px;
  color: #818181;
  display: block;
  transition: 0.3s;
}

/*.overlay-content {
  position: relative;
  top: 25%;
  width: 100%;
  text-align: center;
  margin-top: 30px;
}
*/
/*.overlay a {
  padding: 8px;
  text-decoration: none;
  font-size: 36px;
  color: #818181;
  display: block;
  transition: 0.3s;
}*/

.left-menu ul li a {
  font-family: "Big Shoulders", sans-serif;
  text-transform: uppercase;
  font-size: 48px;
  font-weight: 700;
  color: #fff;
  line-height: 65px;
  padding-left: 30px;
}
.left-menu ul li.active a {
  color: #8e60a9;
}
.left-menu ul li a:hover {
  color: #8e60a9;
}
.overlay a:hover,
.overlay a:focus {
  color: #f1f1f1;
}
.overlay .closebtn {
  position: absolute;
  top: 8px;
  left: 20px;
  font-size: 40px;
  cursor: pointer;
  z-index: 111;
}

.left-menu {
  max-width: 35%;
  min-width: 35%;
}
.right-menu {
  display: flex;
}
.right-menu .right-menu-col-1 {
  padding: 70px;
  /* min-height: 36%; */
  /* max-height: 36%; */
  background: url(../../assets/img/nav-bg-shadow.png);
  background-size: cover;
  background-repeat: no-repeat;
}
.right-menu .right-menu-col-2 {
  padding: 70px;
  background: url(../../assets/img/nav-bg-shadow.png);
  background-size: cover;
  background-repeat: no-repeat;
}
.right-menu .right-menu-col-1 img,
.right-menu .right-menu-col-2 img {
  min-height: 100%;
  max-height: 100%;
  transition: transform 0.5s ease-in-out;
}
.right-menu .right-menu-col-1 img:hover,
.right-menu .right-menu-col-2 img:hover {
  transform: scale(1.1);
}

/*======= left folting menu =======*/

/*=======  Cta Css  =======*/

.middile-banner {
  position: relative;
  z-index: 30; /* foreground curtain layer */
  background: #000;
  width: 100%;
  height: 100vh; /* full viewport height including navbar space */
  min-height: 100vh;
  overflow: hidden;
}

/* Remove extra spacing on hero section */
.middile-banner.section-gap {
  padding-top: 0;
  padding-bottom: 0;
}

.middile-banner-box {
  padding: 15% 25% 10%;
  height: 100vh;
}

/* About section positioning - fixed background layer for curtain effect */
.about-section-three {
  position: relative; /* Will be changed to fixed by GSAP */
  z-index: 20; /* background layer - below hero curtain */
  background: #000;
  min-height: 100vh;
  width: 100%;
  opacity: 1; /* Start fully visible, will fade out during animation */
}

/* Ensure proper spacing for content */
.about-section-three.section-gap {
  padding-top: 150px; /* Increased top padding to move content down */
  padding-bottom: 400px; /* Increased bottom padding for more space below content */
}

/* Ensure smooth transition between sections */
/* Scroll container for the 4-phase animation sequence */
.scroll-container {
  position: relative;
  width: 100%;
  z-index: 100; /* High z-index for animation container */
}

/* Ensure proper stacking for the animation */
.scroll-container .middile-banner {
  position: relative;
  z-index: 500;
}

.scroll-container .about-section-three {
  position: relative;
  z-index: 100;
}

/* Mobile section within scroll container for proper layering */
.scroll-container .mobile-section-wrapper {
  position: relative;
  z-index: 5; /* Behind hero (500) and about (100) sections */
}

.fixed-section {
  display: none !important; /* remove spacer between hero and about */
}

/* Ensure sections after scroll animation are visible with proper styling */
.scroll-animation-wrapper ~ section {
  position: relative !important;
  z-index: 300 !important; /* High z-index above animation elements */
  background: #000 !important; /* Black background to match site theme */
  background-color: black !important;
  visibility: visible !important;
  display: block !important;
}

/* Target specific sections that come after about-section-three */
.scroll-animation-wrapper ~ .section-gap,
.scroll-animation-wrapper ~ .section-gap-none {
  position: relative !important;
  z-index: 350 !important;
  background: #000 !important;
  background-color: black !important;
}

/* Animation spacer styling */
.animation-spacer {
  position: relative;
  z-index: 1;
  background: transparent;
}

/* Performance optimizations for animations */
#aboutVideo {
  will-change: transform, opacity, scale;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  transform-origin: center center;
}

/* Ensure smooth transitions during animation phases */
.scroll-container .about-section-three {
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.scroll-container .middile-banner {
  will-change: transform;
  backface-visibility: hidden;
}

/* Mobile-specific performance optimizations */
@media (max-width: 768px) {
  .middile-banner,
  .about-section-three,
  #aboutVideo,
  .mob-floating-left,
  .mob-floating-right {
    will-change: transform, opacity;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .middile-banner-box {
    padding: 10% 15% 5%;
  }

  /* HIDE MOBILE SECTION COMPLETELY TO ELIMINATE SPACING ISSUES */
  .mobile-section-wrapper {
    display: none !important;
  }

  .mob {
    display: none !important;
  }

  /* Dynamic viewport height for mobile */
  .middile-banner,
  .about-section-three {
    height: calc(var(--vh, 1vh) * 100);
  }

  #aboutVideo {
    width: 280px;
    height: 157px;
  }
}
/* .fixed-section {
    max-width: 100%;
    min-width: 100%;
    min-height: 450px;
    max-height: 450px;
    position: relative;
}
.fixed-section .fixed-full {
    min-width: 100%;
    min-height: 450px;
    max-height: 450px;
    background-attachment: fixed !important;
    background-size: cover !important;
} */
.folting-box {
  min-width: 200px;
  max-width: 200px;
}
.folting-left {
  position: absolute;
  bottom: -150px; /* Moved even much further down */
  left: 0;
  transform: translate(-250px, 40px);
}
.folting-right {
  position: absolute;
  bottom: -150px; /* Moved even much further down */
  right: 0;
  transform: translate(250px, -125px);
}
.title-big h2 {
  text-transform: uppercase;
  font-size: 65px;
  font-weight: 400;
}
.divider-section {
  border-bottom: 2px #ffffff50 solid;
  padding-bottom: 10px;
}

.scroll-container {
  height: 100vh;
  overflow-y: scroll;
  scroll-snap-type: y mandatory;
}

.full-scroll {
  height: 100vh;
  scroll-snap-align: center;
}

/* Other styles */
.full-scroll {
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  /*  background-color: #da1e62;*/
}
.right-scroll-box img {
  max-width: 100%;
  min-width: 100%;
}
/*.full-scroll h2 {
  opacity: 0;
  transition: all 3000ms;
}

.full-scroll.is-visible h2 {
  opacity: 1;
  transform: scale(1.25);
}*/

.scroll-container::-webkit-scrollbar {
  background-color: #ccc;
  width: 1px;
}

.scroll-container::-webkit-scrollbar-thumb {
  background-color: #7531ad61;
}

.left-scroll-box h2 {
  text-transform: uppercase;
  font-size: 52px;
  font-weight: 400;
  margin-bottom: 30px;
}
.left-scroll-box ul li {
  line-height: 45px;
  text-transform: uppercase;
}
.left-scroll-box ul li .text-grey {
  color: #ffffff50;
}
.left-scroll-box .main-btn {
  margin-top: 45px;
}

/*.slick-arrow {
  position: absolute;
  top:50%;
  margin-top: -20px;
  z-index: 10;
}

.slick-prev {
  left: -50px;
}

.slick-next {
  right: -50px;
}

.button-group .active .btn {
  color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}

.slick,
.slick-wrap {
  position: relative;
}*/

.mCustomScrollBox
  + .mCSB_scrollTools
  + .mCSB_scrollTools.mCSB_scrollTools_horizontal,
.mCustomScrollBox + .mCSB_scrollTools.mCSB_scrollTools_horizontal {
  bottom: -50px !important;
  left: 38px;
}
.mCS-dark-thick.mCSB_scrollTools .mCSB_draggerRail {
  background-color: #fff;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
}

.mcs-horizontal {
  margin-left: 60px;
}

.mcs-horizontal .slick-list [data-slick-index="0"] {
  margin-left: 30px;
}
.footer-link ul li a {
  color: #fff;
  font-size: 14px;
  line-height: 45px;
  text-transform: uppercase;
}
.footer-link ul li a:hover {
  color: #666;
}
.footer-socail ul li {
  display: inline-block;
  max-width: 25px;
  margin: 0px 8px 0 0;
}
.footer-socail h5 {
  color: #fff;
  font-family: "Host Grotesk", sans-serif;
  font-size: 14px;
  text-transform: uppercase;
  line-height: 45px;
  font-weight: 400;
  margin-bottom: 4px;
}
.footer-logo {
  max-width: 170px;
}
.footer-top {
  padding-bottom: 30px;
}
.footer-bottom {
  padding: 10px 0;
  border-top: 1px #ffffff50 solid;
}
.footer-bottom .copyright-text {
  color: #ffffff50;
  font-size: 14px;
}
.footer-section {
  padding-top: 45px;
  border-top: 1px #ffffff55 solid;
  margin-top: 50px;
}

.sLider {
  position: relative;
  margin: 180px auto;
  overflow: visible;
  perspective: 350px;
  /* background: #000; */
}

.slide {
  text-align: center;
  color: #fff;
  width: 100%;
  height: 100%;
  font-family: Impact;
  font-size: 96px;
  animation-name: slideList;
  animation-duration: 20s;
  animation-iteration-count: infinite;
  transition-timing-function: ease-in-out;
  position: absolute;
  bottom: 0px;
  transform-origin: bottom;
  transform: rotateX(90deg);
  opacity: 0;
}

.slide1 {
  background-color: #ef7b00;
  animation-delay: 0s;
  z-index: 5;
}
/*
.slide:after {content:"";display:block;width:32px;height:32px;position:absolute;bottom:-32px;background:#fff}
.slide1:after {background-color: #ef7b00;}
.slide2:after {background-color: #58bacc;left:32px}
.slide:hover {z-index:999;animation-play-state:stop}
*/
.slide2 {
  /*  background-color: #58bacc;*/
  animation-delay: 4s;
  z-index: 4;
}

.slide3 {
  /*  background-color: #67b145;*/
  animation-delay: 8s;
  z-index: 3;
}

.slide4 {
  /*  background-color: #7a3e91;*/
  animation-delay: 12s;
  z-index: 2;
}

.slide5 {
  /*  background-color: #e41a20;*/
  animation-delay: 16s;
  z-index: 1;
}

@keyframes slideList {
  0% {
    transform: rotateX(-90deg);
    opacity: 0;
    transform-origin: top;
  }
  3% {
    transform: rotateY(0deg);
    opacity: 1;
    z-index: 6;
  }
  18% {
    transform: rotateX(0deg);
    opacity: 1;
    z-index: 6;
  }
  23% {
    transform: rotateX(90deg);
    opacity: 0;
    transform-origin: bottom;
  }
  100% {
    transform: rotateX(90deg);
    opacity: 0;
    transform-origin: bottom;
  }
}

@-webkit-keyframes slideList {
  0% {
    -webkit-transform: rotateX(-90deg);
    opacity: 0;
    -webkit-transform-origin: top;
  }
  3% {
    -webkit-transform: rotateY(0deg);
    opacity: 1;
    z-index: 6;
  }
  18% {
    -webkit-transform: rotateX(0deg);
    opacity: 1;
    z-index: 6;
  }
  23% {
    -webkit-transform: rotateX(90deg);
    opacity: 0;
    -webkit-transform-origin: bottom;
  }
  100% {
    -webkit-transform: rotateX(90deg);
    opacity: 0;
    -webkit-transform-origin: bottom;
  }
}

/*optional*/

.slideContent {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

/*.creative-fullpage--slider{
  background-color: #ffffff;
  z-index: 2;
  width: 100%;
  position: relative;
  flex-direction: column;
  height: 100vh;
  font-size: 16px;
  display: flex;
  clip-path: none !important;
}*/
.creative-fullpage--slider .slider-inner {
  height: 100vh;
  position: relative;
}
.creative-fullpage--slider .swiper-slide {
  position: relative;
  display: flex;
  justify-content: center;
  text-align: left;
  flex-direction: column;
  overflow: hidden;
}
.creative-fullpage--slider .swiper-slide .slider-inner img {
  object-fit: cover;
  width: 100%;
  /*  height: 100vh;*/
}
.creative-fullpage--slider .swiper-slide .slider-inner video {
  object-fit: cover;
  width: 100%;
  height: 100%;
}
.creative-fullpage--slider .swiper-slide .slider-inner .swiper-content {
  position: absolute;
  top: 22%;
  left: 50px;
  z-index: 1;
}
/*.creative-fullpage--slider .swiper-slide .slider-inner::after {
  content: "";
  position: absolute;
  width: 101%;
  height: 100%;
  top: 0;
  left: -1px;
  background-color: transparent;
  background-image: radial-gradient(at center right, #FFFFFF00 50%, #00000096 100%);
}*/
.swiper-slide .slider-inner .swiper-content .title-area .tag {
  color: #ffffff;
  font-weight: 900;
  font-size: 24px;
  margin-bottom: 10px;
  margin-top: 0px;
}
.swiper-slide .slider-inner .swiper-content .title-area .title {
  margin-top: 50px;
  font-size: 55px;
  color: #fff;
  font-weight: 600;
  line-height: 1.1;
  text-transform: uppercase;
  text-decoration: none;
  margin-bottom: 20px;
}
.swiper-slide .slider-inner .swiper-content ul li {
  font-size: 16px;
  width: 100%;
  font-weight: 400;
  line-height: 32px;
  color: #ffffffb0;
  text-transform: uppercase;
}
.swiper-slide .slider-inner .swiper-content p.disc {
  font-size: 16px;
  width: 100%;
  margin-top: 15px;
  margin: 20px 0px 40px 0px;
  font-weight: 400;
  line-height: 26px;
  color: #ffffffb0;
}
.creative-btn--wrap .creative-slide--btn {
  color: #ffffff;
  margin-left: 18px;
  font-size: 1em;
  transition: margin-left 300ms cubic-bezier(0.49, 0, 0.01, 1);
  font-weight: 400;
  display: inline-flex;
  position: relative;
  white-space: nowrap;
  text-decoration: none;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  user-select: none;
  outline: none;
  outline-color: transparent;
  box-shadow: none;
  will-change: transform;
  backface-visibility: hidden;
  margin-top: 50px;
}

.creative-btn--circle .circle {
  position: absolute;
  right: calc(100% - 10px);
  top: 0;
  bottom: 0;
  margin: auto;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  clip-path: circle(25% at 50% 50%);
  transition: clip-path 500ms cubic-bezier(0.49, 0, 0.01, 1);
}
.creative-btn--circle .circle .circle-fill {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: 100%;
  background-color: #ffffff;
  will-change: transform;
  transform: scale(0);
  z-index: 1;
  transition: transform 500ms cubic-bezier(0.49, 0, 0.01, 1),
    background-color 500ms cubic-bezier(0.49, 0, 0.01, 1);
}
.creative-btn--circle .circle-icon {
  transform: translate(-100%, 0%);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  z-index: 2;
  transition: all 500ms cubic-bezier(0.49, 0, 0.01, 1);
}
.creative-btn--circle .circle-icon .icon-arrow {
  width: 20px;
  height: 20px;
  stroke: none;
  fill: #000;
}
.creative-btn--circle .circle-outline {
  fill: transparent;
  width: 10px;
  stroke: #ffffff;
}
.creative-btn--wrap .creative-slide--btn .creative-btn--label {
  margin-left: 4pt;
  transition: transform 500ms cubic-bezier(0.49, 0, 0.01, 1);
}
.creative-btn--wrap .creative-slide--btn .creative-btn__border {
  position: absolute;
  left: 4pt;
  right: 0;
  bottom: 0;
  height: 1px;
  background: currentColor;
  transform-origin: right;
  transition: transform 500ms cubic-bezier(0.49, 0, 0.01, 1);
}
.creative-btn--wrap .creative-slide--btn:hover .creative-btn--label {
  transform: translateX(18px);
}
.creative-btn--wrap .creative-slide--btn:hover .creative-btn__border {
  transform: scale(0, 1);
}
.creative-btn--wrap .creative-slide--btn:hover {
  margin-left: 38px !important;
}
.creative-btn--wrap .creative-slide--btn:hover .circle {
  clip-path: circle(50% at 50% 50%);
}
.creative-btn--wrap .creative-slide--btn:hover .circle-fill {
  transform: scale(1, 1);
}
.creative-btn--wrap .creative-slide--btn:hover .circle-icon {
  transform: translate(0%, 0%);
  opacity: 1;
}
/*.creative-fullpage--slider .swiper-container-h .swiper-button-next, 
.creative-fullpage--slider .swiper-container-h .swiper-button-prev {
  bottom: 5%;
  top: unset;
  transform: scale(1);
  transition: all 0.4s;
  background-color: #FFFFFF00;
  backdrop-filter: blur(20px);
  height: 85px;
  width: 85px;
  line-height: 85px;
  border-radius: 50%;
  transition: all 0.4s;
}
.creative-fullpage--slider .swiper-container-h .swiper-button-next {
  right: 50px;
}
.creative-fullpage--slider .swiper-container-h .swiper-button-prev {
  left: 50px;
}
.swiper-container-h .slider-pagination-area {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: unset;
  right: unset;
  bottom: 80px;
  left: 50% !important;
  transform: translateX(-50%);
  width: 500px;
  z-index: 1;
}
.swiper-container-h .slider-pagination-area .slide-range {
  font-size: 16px;
  font-weight: 500;
  margin: 0 15px;
  color: #ffffff;
  line-height: 0;
  position: absolute;
  font-size: 20px;
}
.swiper-container-h .slider-pagination-area .slide-range.one {
  left: -50px;
}
.swiper-container-h .slider-pagination-area .slide-range.three {
  right: -50px;
}
.swiper-container-h .slider-pagination-area .swiper-pagination {
  bottom: 0 !important;
  width: 500px !important;
}
.swiper-container-h .slider-pagination-area .swiper-pagination .swiper-pagination-progressbar-fill {
  background: #ffffff;
}
.swiper-container-h .swiper-button-next::after {
  content: "\f061";
  font-family: var(--fa-style-family, "Font Awesome 6 Free");
  font-weight: var(--fa-style, 900);
  background: none;
  color: #ffffff;
  font-size: 60px;
}
.swiper-container-h .swiper-button-prev::after {
  content:"\f060";
  font-family: var(--fa-style-family, "Font Awesome 6 Free");
  font-weight: var(--fa-style, 900);
  background: none;
  color: #ffffff;
  font-size: 60px;
}
.swiper-container-h .swiper-button-next:hover, 
.swiper-container-h .swiper-button-prev:hover {
  background: #FFFFFF0D;
}*/

.content-slider-middle {
  display: flex;
  max-width: 95%;
}
.content-slider-middle .content-slider-middle-left {
  max-width: 50%;
  min-width: 50%;
}
.content-slider-middle .content-slider-middle-right {
  max-width: 50%;
  min-width: 50%;
  position: relative;
}

.image-container {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  display: inline-block;
  width: 100%;
}

.image-container img {
  width: 100%;
  height: auto;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  display: block;
}

.view-more-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  font-size: 13px;
  z-index: 1; /* ensure below #aboutVideo when scaling */
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  backdrop-filter: blur(10px);
  z-index: 10;
  opacity: 0.85;
  transform: scale(1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-hover-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  width: 140px;
  height: 50px;
  border-radius: 30px;
  font-size: 14px;
  z-index: 1; /* ensure below #aboutVideo when scaling */
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  backdrop-filter: blur(20px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
  pointer-events: none;
  z-index: 11;
  cursor: pointer;
}

.view-more-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
  opacity: 0.95;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5);
}

/* Hover effects for image container */
.image-container:hover img {
  filter: blur(4px);
  transform: scale(1.08);
}

.image-container:hover .view-more-btn {
  opacity: 0;
  transform: scale(0.2);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.image-container:hover .view-hover-btn {
  opacity: 0.95;
  transform: translate(-50%, -50%) scale(1);
  pointer-events: auto;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.view-hover-btn:hover {
  transform: translate(-50%, -50%) scale(1.15);
  background: rgba(0, 0, 0, 0.85);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
  opacity: 0.9;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .view-more-btn {
    top: 10px;
    right: 10px;
    width: 70px;
    height: 70px;
    font-size: 11px;
  }
  .mobile-section-wrapper {
    display: none;
  }

  .view-hover-btn {
    width: 120px;
    height: 44px;
    font-size: 12px;
    border-radius: 22px;
  }
}

@media (max-width: 480px) {
  .view-more-btn {
    top: 8px;
    right: 8px;
    width: 60px;
    height: 60px;
    font-size: 10px;
  }
  .mobile-section-wrapper {
    display: none;
  }

  .view-hover-btn {
    width: 100px;
    height: 40px;
    font-size: 11px;
    border-radius: 20px;
  }
}

.full-gallery img {
  max-width: 100%;
  min-width: 100%;
}

.progress {
  display: block;
  width: 95%;
  margin-left: 6%;
  left: 20%;
  height: 10px;
  border-radius: 10px;
  overflow: hidden;
  background-color: #f5f5f5;
  background-image: linear-gradient(to right, #424242, #2c2626);
  background-repeat: no-repeat;
  background-size: 0 100%;
  transition: background-size 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
  margin-top: 30px;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
.post-slider {
  margin-left: 70px;
}
.post-slider .slick-list [data-slick-index="0"] {
  margin-left: 10px;
}

/* ====================== Responsive Ipad =============================== */
@media (max-width: 991px) {
  .creative-fullpage--slider .swiper-slide .slider-inner .swiper-content {
    width: 100%;
    text-align: center;
    left: 0;
  }
  .mobile-section-wrapper {
    display: none;
  }
  /*.creative-fullpage--slider .swiper-container-h .swiper-button-next, 
  .creative-fullpage--slider .swiper-container-h .swiper-button-prev {
    height: 50px;
    width: 50px;
    line-height: 50px;
  }
  .swiper-container-h .slider-pagination-area{
    width: 200px !important;
  }
  .swiper-container-h .swiper-button-next::after, 
  .swiper-container-h .swiper-button-prev::after {
    font-size: 30px;
  }
  .creative-fullpage--slider .swiper-container-h .swiper-button-next, 
  .creative-fullpage--slider .swiper-container-h .swiper-button-prev{
    background: #ffffff3b;
  }
  .swiper-container-h .slider-pagination-area .swiper-pagination {
    bottom: 0 !important;
    width: 200px !important;
  }*/
}

/* ====================== Responsive Iphone =============================== */

@media (max-width: 767px) {
  .mcs-horizontal {
    margin-left: 0px;
  }
  .mobile-section-wrapper {
    display: none;
  }
  .mcs-horizontal .slick-list [data-slick-index="0"] {
    margin-left: 0px;
  }
  .main-content-img {
    padding: 10px 30px;
  }
  .folting-box {
    min-width: 140px;
    max-width: 140px;
  }
  .folting-right {
    transform: translate(-8px, 10px); /* Moved up even less to reduce overlap significantly */
  }
  .folting-left {
    transform: translate(15px, 148px); /* Moved down much more to give maximum space */
  }
  .left-scroll-box {
    margin-bottom: 30px;
  }
  .left-menu ul li a {
    font-size: 30px;
    line-height: 55px;
  }
  .left-menu {
    max-width: 100%;
    min-width: 100%;
  }
  .overlay-content {
    display: block;
  }
  .right-menu .right-menu-col-1 {
    padding: 15px;
  }
  .right-menu .right-menu-col-2 {
    padding: 15px;
  }
  .right-menu .right-menu-col-2 img {
    min-height: 100%;
    max-height: 100%;
    transform: translate(-4px, 25px);
  }
  .overlay-content {
    margin-top: 60px;
  }
  .overlay .closebtn {
    left: 0px;
  }
  .mhid{
    display: none;
  }

  .middile-banner-box .sLider {
    margin: 60px auto;
  }
  .fixed-section .fixed-full {
    min-width: 100%;
    min-height: 250px;
    max-height: 250px;
    background-size: cover !important;
  }
  .fixed-section {
    min-height: 250px;
    max-height: 250px;
  }
  .content-slider-middle {
    max-width: 100%;
    flex-direction: column-reverse;
  }
  .content-slider-middle .content-slider-middle-left {
    max-width: 100%;
    min-width: 100%;
  }
  .content-slider-middle .content-slider-middle-right {
    max-width: 100%;
    min-width: 100%;
  }
  .creative-btn--wrap .creative-slide--btn {
    margin-top: 30px;
  }
  .post-slider {
    margin-left: 0px;
  }
  .progress {
    display: block;
    width: 100%;
    margin-left: 0%;
  }
  .post-slider .slick-list [data-slick-index="0"] {
    margin-left: 0px;
  }
}

/* REMOVED: Legacy .post-section rules that conflict with .blog-carousel-section implementation */

/* Smooth transitions for blog items */
.blog-items {
  background: #1f1f1f;
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  border-radius: 8px;
  overflow: hidden;
}

.blog-items:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.blog-items .blog-img {
  min-height: 270px;
  max-height: 270px;
  max-width: 385px;
  min-width: 385px;
  overflow: hidden;
}

.blog-items .blog-img img {
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.blog-items:hover .blog-img img {
  transform: scale(1.08);
}

.blog-items {
  max-width: 385px;
  min-width: 385px;
  margin: 0 10px;
}

.blog-items .blog-content {
  padding: 20px;
}

.blog-items .blog-content p {
  font-size: 15px;
  padding: 12px 0;
  color: #ffffff50;
  line-height: 22px;
}

.blog-items .blog-content .post-mata {
  font-size: 15px;
  color: #ffffff50;
  line-height: 22px;
}

.blog-items .blog-content h4 {
  color: #fff;
  margin-bottom: 10px;
  transition: color 0.3s ease;
}

.blog-items:hover .blog-content h4 {
  color: #fff;
}

/* REMOVED: More legacy .post-section rules that conflict with .blog-carousel-section */

/* REMOVED: Large block of .post-section rules that don't apply to .blog-carousel-section */

/* REMOVED: More .post-section rules that conflict with .blog-carousel-section */

/* REMOVED: All .post-section .blog-items rules that conflict with .blog-carousel-section */

/* REMOVED: Desktop .post-section responsive rules */

/* REMOVED: Tablet .post-section responsive rules */

/* REMOVED: Mobile .post-section responsive rules */

/* REMOVED: Final .post-section performance rules */

/* OUR WORK SECTION OPTIMIZATION */

/* DESKTOP: Our Work section - LET INLINE CSS HANDLE STYLING */
@media (min-width: 769px) {
  /* Minimal CSS - let inline styles from index.html work like b2.html */
  section.work-transition-section {
    z-index: 40; /* Only set z-index, let inline CSS handle the rest */
  }

  /* Remove all conflicting CSS - let inline styles work */
  .work-content-container {
    position: relative;
    height: 100vh;
    width: 100%;
  }
}

/* BLOG SECTION SCROLL AREA OPTIMIZATION */

/* DESKTOP: Increased scroll area for better horizontal scroll animation */
@media (min-width: 769px) {
  .blog-carousel-section {
    height: 600vh !important; /* Increased from 400vh to 600vh for more desktop scroll area */
  }

  .blog-sticky-container {
    z-index: 50; /* Ensure proper layering above footer */
  }
}

/* MOBILE: Z-index fix and preserve mobile heights from main.css */
@media (max-width: 768px) {
  /* Fix footer z-index conflict with main.css */
  .footer-section {
    z-index: 10 !important; /* Override main.css z-index: 100 that was causing overlap */
  }

  .blog-sticky-container {
    z-index: 50 !important; /* Ensure blog section stays above footer on mobile */
  }

  /* Our Work section mobile optimization */
  .work-transition-section {
    position: relative;
    z-index: 40; /* Proper layering on mobile */
  }

  .work-sticky-container {
    z-index: 41 !important; /* Ensure proper layering on mobile */
  }
}

/* ===== SCROLL TO VIEW INDICATOR ===== */

.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  z-index: 100; /* Above hero content but below curtain animation */
  color: #ffffff;
  opacity: 0.8;
  transition: all 0.3s ease;
  cursor: default;
}

.scroll-indicator:hover {
  opacity: 1;
  transform: translateX(-50%) translateY(-5px);
}

.scroll-text {
  font-size: 18.77px;
  font-weight: 300;
  letter-spacing: 1px;
  /* text-transform: uppercase; */
  margin-bottom: 4px;
  font-family: big shoulders;
  line-height: 24.22px;
}

.scroll-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transition: all 0.3s ease;
  animation: scrollBounce 2s infinite;
}

.scroll-arrow svg {
  width: 16px;
  height: 16px;
  color: #ffffff;
  transition: transform 0.3s ease;
}

.scroll-indicator:hover .scroll-arrow {
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
}

.scroll-indicator:hover .scroll-arrow svg {
  transform: translateY(2px);
}

/* Bounce animation for the scroll arrow */
@keyframes scrollBounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8px);
  }
  60% {
    transform: translateY(-4px);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .scroll-indicator {
    bottom: 80px; /* Increased for better mobile visibility */
  }

  .scroll-text {
    font-size: 12px;
  }

  .scroll-arrow {
    width: 28px;
    height: 28px;
  }

  .scroll-arrow svg {
    width: 14px;
    height: 14px;
  }
}

@media (max-width: 480px) {
  .scroll-indicator {
    bottom: 100px; /* Further increased for small mobile devices */
  }
  .mobile-section-wrapper {
    display: none;
  }

  .scroll-text {
    font-size: 18.77px;
    letter-spacing: 0.5px;
  }

  .scroll-arrow {
    width: 24px;
    height: 24px;
  }

  .scroll-arrow svg {
    width: 12px;
    height: 12px;
  }
}

#latestPostSlider {
  width: 100vw !important;
}
#latestPostSlider div {
  width: 100% !important;
}
#blogs {
  margin-left: 10%;
}

@media (max-width: 847px) {
  .middile-banner-box {
    padding: 313px 2% 10%;
    height: 100vh;
  }
}

@media (max-width: 768px) {
  .middile-banner-box {
    padding: 313px 2% 10%;
  }
  .mobile-section-wrapper {
    display: none;
  }
}

@media (max-width: 341px) {
  .header-right-icon ul li {
    height: 19.36px;
    width: 19.36px;
    font-size: 12px;
  }

  .site-logo {
    padding: 15px 0 10px;
    max-width: 120px;
  }
  /* REMOVED: .scroll-container .about-section-three { display: none !important; } - was hiding about section on mobile */
}

@media (max-width: 450px) {
  /* REMOVED: .scroll-container .about-section-three { display: none !important; } - was hiding about section on mobile */

  /* REMOVED: Conflicting .post-section rules - blog uses .blog-carousel-section instead */

  /* ENSURE MOBILE SECTION IS HIDDEN ON SMALL SCREENS */
  .mobile-section-wrapper,
  .mob {
    display: none !important;
  }
  .mob h2 {
    text-align: center;
    font-family: Big Shoulders;
    font-weight: 700;
    font-size: 32.35px;
    line-height: 41.75px;
    padding-bottom: 42px;
  }
  .mob img {
    width: 337.78px;
    height: 190px;
    margin-left: 28px;
    border: white 1.16px solid;
  }
  .mob p {
    font-family: url(../fonts/fa-regular-400.woff2);
    font-size: 15.67px;
    text-align: center;
    margin-left: 27px;
    height: 145px;
    width: 339px;
    padding-top: 42px;
  }
  .mob button {
    height: 42.1px;
    width: 134.33px;
    margin-left: 129.67px;
    margin-top: 110px;
    border: white 0.47px solid;
    background: transparent;
    font-family: url(../fonts/fa-regular-400.woff2);
    color: white;
    font-weight: 600;
    font-size: 13.24px;
    -webkit-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
    position: relative;
    z-index: 2;
    cursor: pointer;
  }
  .mob button:hover {
    color: #fff;
    background-color: #7531ad61;
    border: 1px #7531ad61 solid;
    opacity: 0.7;
  }
  .mob-floating-left img {
    width: 157.23px;
    height: 176.24px;
  }
  .mob-floating-right img {
    width: 200px;
    height: 171.91px;
  }
}
/* REMOVED: Last .post-section rule */
@media screen and (min-width: 300px) and (max-width: 311px) {
  .mob-floating-right img {
    width: 130.28px;
    height: 146.91px;
  }

  .mob-floating-left img {
    width: 130.23px;
    height: 146.24px;
  }

  .mob img {
    width: 250.78px;
    height: 170px;
    margin-left: 28px;
    border: white 1.16px solid;
  }
  .mob p {
    font-family: url(../fonts/fa-regular-400.woff2);
    font-size: 12.67px;
    text-align: center;
    /* margin-left: 27px; */
    height: 145px;
    width: 250px;
    padding-top: 42px;
  }

  .mob h2 {
    text-align: center;
    font-family: Big Shoulders;
    font-weight: 700;
    font-size: 25.35px;
    line-height: 41.75px;
    padding-bottom: 42px;
  }

  .mob button {
    height: 42.1px;
    width: 134.33px;
    margin-left: 85.67px;
    margin-top: 110px;
    border: white 0.47px solid;
    background: transparent;
    font-family: url(../fonts/fa-regular-400.woff2);
    color: white;
    font-weight: 600;
    font-size: 13.24px;
    -webkit-transition: all 0.3s ease-out 0s;
    transition: all 0.3s ease-out 0s;
    position: relative;
    z-index: 2;
    cursor: pointer;
  }

  @media screen and (min-width: 311px) {
  }
}

/* ===== MOBILE SECTION BASE STYLING ===== */
/* Mobile section wrapper - positioned within scroll container for proper layering */
.mobile-section-wrapper {
  position: relative; /* Normal document flow positioning */
  top: auto;
  left: auto;
  width: 100%;
  height: auto; /* Let it size naturally */
  z-index: auto; /* Normal z-index in document flow */
  background: #000;
  display: none; /* Hidden by default, shown only on mobile via JS */
  overflow: hidden;
  margin-top: -50vh; /* Pull section up to reduce scroll distance */
  padding-top: 50vh; /* Maintain internal spacing */
}

@media (max-width: 768px) {
  /* HIDE MOBILE SECTION WRAPPER COMPLETELY */
  .mobile-section-wrapper {
    display: none !important;
  }

  /* Force remove pin spacing on mobile devices - TARGETED RULES */
  /* Only target hero section pin spacer, not Our Work section */
  .scroll-container .middile-banner + .pin-spacer,
  .scroll-container .about-section-three + .pin-spacer {
    height: 0 !important;
    min-height: 0 !important;
    max-height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /* ENSURE Our Work section pin spacer works correctly */
  .scroll-container .work-transition-section .pin-spacer {
    height: auto !important; /* Allow GSAP to set proper height */
    min-height: auto !important;
    max-height: none !important;
    display: block !important;
    position: relative !important;
  }

  /* Ensure scroll container behaves normally on mobile */
  .scroll-container {
    height: auto !important;
    min-height: auto !important;
  }

  /* ENSURE PROPER BLOG-TO-FOOTER TRANSITION ON MOBILE */
  .footer-section {
    position: relative;
    z-index: 10; /* Lower than blog section to prevent overlap */
    margin-top: 0; /* Remove any top margin that might cause gaps */
  }
}

.mob {
  position: relative; /* Normal positioning within wrapper */
  top: auto;
  left: auto;
  width: 100%;
  height: auto; /* Let it size naturally */
  z-index: auto; /* Normal z-index */
  background: #000;
  padding: 150px 20px 80px; /* Increased top padding for more space */
  min-height: 100vh; /* Minimum full height but can be taller */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  max-width: 100%;
  overflow: visible; /* Allow content to be visible */
}

/* Mobile floating images - now children of .mob */
.mob-floating-left {
  position: absolute;
  z-index: 7; /* Above mob content for visibility */
  left: 20px; /* Positioned relative to .mob container */
  bottom: 120px; /* FIXED: Positioned above button area but below main content */
  transform: translateY(0);
  opacity: 0;
  transition: opacity 0.6s ease, transform 0.6s ease;
  pointer-events: none; /* Don't interfere with content */
}

.mob-floating-right {
  position: absolute;
  z-index: 7; /* Above mob content for visibility */
  right: 20px; /* Positioned relative to .mob container */
  bottom: 80px; /* FIXED: Positioned above button area but below main content with offset */
  transform: translateY(0);
  opacity: 0;
  transition: opacity 0.6s ease, transform 0.6s ease;
  pointer-events: none; /* Don't interfere with content */
}

/* Animation classes for mobile floating images */
.mob-floating-left.animate-in {
  opacity: 1;
  transform: translateY(0) translateX(0);
}

.mob-floating-right.animate-in {
  opacity: 1;
  transform: translateY(0) translateX(0);
}

/* Mobile-specific positioning and visibility */
@media (max-width: 768px) {
  .middile-banner {
    position: relative;
    z-index: 30; /* Above about section for shutter effect */
    background: #000; /* Ensure solid background for curtain effect */
  }
  
  .about-section-three {
    position: relative; /* Will be changed to fixed by GSAP for shutter effect */
    z-index: 20; /* Behind hero section */
    margin-top: 0;
    display: block !important; /* Ensure visibility on mobile */
    opacity: 1 !important; /* Ensure visibility on mobile */
    min-height: 100vh; /* Maintain full height */
  }
  
  /* HIDE MOBILE SECTION ON ALL MOBILE DEVICES */
  .mobile-section-wrapper {
    display: none !important; /* Completely hidden to eliminate spacing issues */
  }

  .mob {
    display: none !important; /* Completely hidden to eliminate spacing issues */
  }

  /* Ensure about section is visible within scroll container on mobile */
  .scroll-container .about-section-three {
    display: block !important; /* Override any hiding rules */
    opacity: 1 !important; /* Ensure visibility */
    /* Position will be controlled by GSAP for shutter effect */
  }

  /* Mobile shutter effect optimizations */
  .middile-banner,
  .about-section-three {
    will-change: transform, opacity; /* Optimize for animations */
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
}

/* Enhanced mobile display rules */
@media (max-width: 1024px) {
  .mobile-section-wrapper {
    display: none !important; /* Hide on tablets and below to eliminate spacing issues */
  }

  .mob {
    display: none !important; /* Hide on tablets and below to eliminate spacing issues */
  }
}

/* Hide mobile section on desktop */
@media (min-width: 1025px) {
  .mobile-section-wrapper {
    display: none !important;
  }
}

/* Responsive adjustments for mobile floating images */
@media (max-width: 450px) {
  .mob-floating-left {
    left: 10px; /* Closer to edge on small screens */
    bottom: 100px; /* Adjusted for smaller screens */
  }

  .mob-floating-right {
    right: 10px; /* Closer to edge on small screens */
    bottom: 60px; /* Adjusted for smaller screens */
  }

  .mob-floating-left img {
    width: 120px;
    height: 135px;
  }

  .mob-floating-right img {
    width: 120px;
    height: 130px;
  }
}

@media screen and (min-width: 300px) and (max-width: 311px) {
  .mob-floating-left {
    left: 5px; /* Very close to edge on very small screens */
    bottom: 90px; /* Adjusted for very small screens */
  }

  .mob-floating-right {
    right: 5px; /* Very close to edge on very small screens */
    bottom: 50px; /* Adjusted for very small screens */
  }
}
